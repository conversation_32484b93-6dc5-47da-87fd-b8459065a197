import csv
import sys

import pandas as pd


def select_link_id_from_sample(
    df_link_id_to_select: pd.DataFrame,
    df_sample: pd.DataFrame,
    field_name_link_id_to_select: str = "LINK_ID",
    field_name_sample: str = "LINK_ID",
) -> pd.DataFrame:
    list_link_id_sample = df_sample[field_name_sample].unique()
    if (
        field_name_link_id_to_select == "LINK-DIR"
        or field_name_link_id_to_select == "LINK_DIR"
    ):
        df_link_id_to_select["LINK_ID"] = [
            link_dir[:-1]
            for link_dir in df_link_id_to_select.loc[:, field_name_link_id_to_select]
        ]
        df_link_id_to_select["LINK_ID"] = df_link_id_to_select["LINK_ID"].astype(int)
        df_selected = df_link_id_to_select[
            df_link_id_to_select["LINK_ID"].isin(list_link_id_sample)
        ].drop(columns="LINK_ID")
    else:
        df_selected = df_link_id_to_select[
            df_link_id_to_select[df_link_id_to_select].isin(list_link_id_sample)
        ]
    return df_selected


def read_csv(path: str) -> pd.DataFrame:
    try:
        with open(path, "r") as csvfile:
            dialect = csv.Sniffer().sniff(csvfile.readline())
            separator = dialect.delimiter
        if separator == ";":
            decimal = ","
        else:
            decimal = "."
        return pd.read_csv(path, sep=separator, decimal=decimal)
    except Exception as e:
        print(f"Erreur lors de la lecture du fichier CSV: {e}")
        raise e


if __name__ == "__main__":
    nb_argv = len(sys.argv)
    if nb_argv != 4 and nb_argv != 6:
        print("ERROR: Expected 3 or 5 arguments, got {:d} instead".format(nb_argv))
        sys.exit(1)
    else:
        path_link_id_to_select = sys.argv[1]
        path_sample = sys.argv[2]
        output_path = sys.argv[3]
        if nb_argv == 6:
            field_name_link_id_to_select = sys.argv[4]
            field_name_sample = sys.argv[5]
        else:
            field_name_link_id_to_select = "LINK_ID"
            field_name_sample = "LINK_ID"
    df_link_id_to_select = read_csv(path_link_id_to_select)
    df_sample = read_csv(path_sample)
    df_new = select_link_id_from_sample(
        df_link_id_to_select, df_sample, field_name_link_id_to_select, field_name_sample
    )
    df_new.to_csv(output_path, index=False)
