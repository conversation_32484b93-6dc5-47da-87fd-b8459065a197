#!/usr/bin/env python
SYSTEM_ARGUMENT_LENGTH = 2

# Email subjects
MAIL_SUBJECT_PROGRAM_ERROR = "Parking Monitoring - Failed To Execute"

# Request status
REQUEST_STATUS_FAILED_TO_EXECUTE = "Failed to execute"

# Logging configuration
LOGGING_PATH = "scripts/parking_monitoring/loggingConfig.log"
LOGGER_CONSOLE = "root"
LOGGER_FILE = "fileHandler"

# Logging messages
LOGGING_START = "Started"
LOGGING_EMAIL_SENT = "Email sent"
LOGGING_FINISH = "Finished"
LOGGING_EMAIL_PROGRAM_ERROR = (
    "Program error AND failing to send error " "notification email"
)

# File extensions
FILE_EXTENSIONS = [".json"]

# Error codes
# In json configuration file
CODE_ERR_INVALID_DICT = 100
CODE_ERR_DICT_MISSING = 101
CODE_ERR_SYNTAX_INVALID = 102
CODE_ERR_INVALID_KEY = 103
CODE_ERR_KEY_MISSING = 104
CODE_ERR_INVALID_VALUE = 105

# Can't open file
CODE_ERR_OUTPUT_FILE_OPEN = 200

# Path invalid
CODE_ERR_LOGGING_CONFIG_PATH = 301
CODE_ERR_JSON_PATH = 302
CODE_ERR_GDB_PATH = 303

# Missing execution parameter
CODE_ERR_EXECUTION_PARAM_MISSING = 600

# Module not found
CODE_ERR_PACKAGE_MISSING = 500

# Task failed
CODE_ERR_EMAIL_PROGRAM_ERROR = 417
CODE_ERR_AGOL_CONNEXION_ERROR = 418
CODE_ERR_UPDATEFL_ERROR = 419

# Exception error codes
CODE_ERR_TYPE_ERR = 800
CODE_ERR_NAME_ERR = 801
CODE_ERR_VALUE_ERR = 802
CODE_ERR_INDEX_ERR = 803

# JSON DICTIONARIES AND KEYS
# Global parameters
GLOBALPARAMETERS_DICT = "globalParameters"
GLOBALPARAMETERS_KEY_UPDATEFEATURELAYER = "updateFeatureLayer"

# Email
EMAIL_DICT = "email"

# ArcGISOnline
AGOL_DICT = "ArcGISOnline"
AGOL_KEY_URL = "arcgisWebsite"
AGOL_KEY_USERNAME = "arcgisUsername"
AGOL_KEY_PWD = "arcgisPassword"
AGOL_KEY_ITEMTYPE = "itemType"
AGOL_KEY_ITEMTITLE = "itemTitle"

# UpdateFeatureLayerParameters
UPDATEFL_DICT = "updateFeatureLayerParameters"

# Layer urls
LAYERURL_KEY_INPUTLAYER = "inputLayer"
LAYERURL_KEY_UPDATELAYER = "updateLayer"

# Level ratios
LEVELRATIOS_DICT = "levelRatios"
