__author__ = "<PERSON><PERSON><PERSON><PERSON> Fourez"
__version__ = "1.1"
__maintainer__ = "<PERSON>hiba<PERSON>"
__email__ = "<EMAIL>"
__status__ = "Production"

import json
import logging
import logging.config
import os
import sys
from typing import Any, Optional


# Classe abstraite dont héritent toutes les classes des outils (gère le reproting)
class Tool:
    def __init__(
        self, json_file: dict, messages: Any = None, logging_path: Optional[str] = None
    ):
        self.warnings = 0
        self.errors = 0
        self.json_file = json_file
        self.messages = messages
        if logging_path is not None:
            self.set_logging(logging_path)  # Mise en place d'un logger

    def print_message(self, text: str) -> None:
        if self.logger_console is not None and self.logger_file is not None:
            self.logger_console.info(text)
            self.logger_file.info(text)
        elif self.messages is not None:
            self.messages.addMessage(text)
        else:
            print(text)

    def print_warning(self, text: str) -> None:
        self.warnings += 1
        if self.logger_console is not None and self.logger_file is not None:
            self.logger_console.warning(text)
            self.logger_file.info(text)
        elif self.messages is not None:
            self.messages.addWarningMessage(text)
        else:
            print("WARNING: ", text)

    def print_error(self, text: str) -> None:
        self.errors += 1
        if self.logger_console is not None and self.logger_file is not None:
            self.logger_console.error(text)
            self.logger_file.info(text)
        elif self.messages is not None:
            self.messages.addErrorMessage(text)
        else:
            print("ERROR: ", text)

    def set_logging(
        self,
        logging_path: str,
        logger_console: str = "root",
        logger_file: str = "fileHandler",
    ) -> None:
        log_path = os.path.abspath(logging_path)
        if os.path.exists(log_path):
            try:
                logging.config.fileConfig(logging_path)
                self.logger_console = logging.getLogger(logger_console)
                self.logger_file = logging.getLogger(logger_file)
            except PermissionError:
                self.print_error(
                    "Output file (logging information event.csv/timer.csv file)"
                    + " is open. No logger will be used"
                )
        else:
            self.print_error(log_path + " doesn't exist. No logger will be used")


# Fonction d'exécution des tools par la console
def main(tool_class: type) -> None:
    json_path = sys.argv[1]
    with open(json_path, encoding="utf-8") as json_data:
        json_file = json.load(json_data)
    tool_object = tool_class(json_file, logging_path="Logger/loggingConfig.log")
    tool_object.execute()
