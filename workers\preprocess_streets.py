import geopandas as gpd
import pandas as pd


def preprocess_street(gdf_street: gpd.GeoDataFrame) -> gpd.GeoDataFrame:
    """
    Pré-traite les données géospatialisées du réseau routier.

    Cette fonction duplique les tronçons routiers du GeoDataFrame d'entrée
    et applique un décalage selon le sens de circulation.

    Parameters:
    -----------
    gdf_street : gpd.GeoDataFrame
        Le GeoDataFrame d'origine contenant les tronçons routiers.

    Returns:
    --------
    gpd.GeoDataFrame
        Un nouveau GeoDataFrame avec les tronçons dirigés et décalés.
    """
    gdf_street_f = gdf_street[gdf_street["DIR_TRAVEL"].apply(lambda x: x in ["F", "B"])]
    gdf_street_t = gdf_street[gdf_street["DIR_TRAVEL"].apply(lambda x: x in ["T", "B"])]
    gdf_street_f["LINK_DIR"] = gdf_street_f["LINK_ID"].astype(str) + "F"
    gdf_street_t["LINK_DIR"] = gdf_street_t["LINK_ID"].astype(str) + "T"
    gdf_street_t["geometry"] = gdf_street_t[
        "geometry"
    ].reverse()  # Inversion de la géométrie pour les tronçons T
    gdf_street_dir = pd.concat([gdf_street_f, gdf_street_t]).set_index(
        "LINK_DIR", drop=True
    )
    gdf_street_dir["geometry"] = gdf_street_dir["geometry"].apply(
        lambda x: x.parallel_offset(0.00005, "right")
    )
    return gdf_street_dir
