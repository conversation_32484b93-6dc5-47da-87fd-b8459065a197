import geopandas as gpd
from shapely.geometry import LineString

from workers.preprocess_streets import preprocess_street


def test_preprocess_streets() -> None:
    geo_dataframe = gpd.GeoDataFrame(
        {
            "TARGET_FID": [123],
            "LINK_ID": [0],
            "ST_NAME": [641861],
            "FEAT_ID": [35321],
            "FUNC_CLASS": [3],
            "SPEED_CAT": [4],
            "FR_SPD_LIM": [80],
            "TO_SPD_LIM": [80],
            "TO_LANES": [1],
            "FROM_LANES": [1],
            "DIVIDER": ["N"],
            "DIR_TRAVEL": ["B"],
        },
        crs="EPSG:4326",
        geometry=[LineString([(45.5, 5.0), (45.6, 5.1)])],
    )

    produced_dataframe = preprocess_street(geo_dataframe)

    assert list(produced_dataframe.index) == ["0F", "0T"]
    assert produced_dataframe.index.names == ["LINK_DIR"]
    assert list(produced_dataframe["DIR_TRAVEL"]) == ["B", "B"]
    assert produced_dataframe["geometry"].iloc[0] == LineString(
        [(45.5, 5.0), (45.6, 5.1)]
    ).parallel_offset(0.00005, "right")
    assert produced_dataframe["geometry"].iloc[1] == LineString(
        [(45.6, 5.1), (45.5, 5.0)]
    ).parallel_offset(0.00005, "right")
