from typing import Dict, List, Set

import geopandas as gpd
import pandas as pd


class RouteProportions:
    def __init__(self, gdf_matched_routes: gpd.GeoDataFrame):
        self.gdf_matched_routes = self._check_matched_routes(gdf_matched_routes)

    def _check_matched_routes(self, routes: gpd.GeoDataFrame) -> gpd.GeoDataFrame:
        required_columns = ["LINK_ID", "ANONID", "remainDistance"]

        for column in required_columns:
            if column not in routes.columns:
                print(routes.columns)
                raise ValueError(
                    f"The input dataframe is expected "
                    f"to have at least columns {required_columns}."
                )

            if routes[column].isnull().any():
                raise ValueError(f"The column {column} should not contain null values.")
        try:
            routes["LINK_ID"] = routes["LINK_ID"].astype("int64")
        except ValueError:
            raise ValueError("The column LINK_ID should contain only integers.")

        return routes

    def compute_incomings_df(self, links_origin: List[int]) -> pd.DataFrame:
        return (
            self.gdf_matched_routes[
                self.gdf_matched_routes["LINK_ID"].isin(links_origin)
            ]
            .drop_duplicates("ANONID")
            .reset_index(drop=True)
        )

    def compute_possible_outgoings(
        self, possible_links_mouvements: Dict[str, List[int]]
    ) -> Set[int]:
        possible_outgoings = set()
        for links in possible_links_mouvements.values():
            gdf_sorted = self.gdf_matched_routes[
                self.gdf_matched_routes["LINK_ID"].isin(links)
            ].sort_values(by="remainDistance", ascending=False)
            possible_outgoings.update(gdf_sorted["ANONID"].unique())
        return possible_outgoings

    def filter_incomings_without_mouvements(
        self, links_origin: List[int], possible_links_mouvements: Dict[str, List[int]]
    ) -> pd.DataFrame:
        incomings = self.compute_incomings_df(links_origin)
        possible_outgoings = self.compute_possible_outgoings(possible_links_mouvements)
        print(f"number of possible outgoings: {len(possible_outgoings)}")
        return incomings[incomings["ANONID"].isin(possible_outgoings)].reset_index(
            drop=True
        )

    def compute_outgoing_proportions(
        self,
        links_origin: List[int],
        possible_links_mouvements: Dict[str, List[int]],
        name_origin: str,
    ) -> pd.DataFrame:
        out = pd.DataFrame()
        possible_outgoings = set()
        gdf_matched_routes_destinations = self.gdf_matched_routes.copy()
        gdf_matched_routes_destinations = gdf_matched_routes_destinations.sort_values(
            by="remainDistance", ascending=False
        )
        filtered_incomings = self.filter_incomings_without_mouvements(
            links_origin, possible_links_mouvements
        )
        print("filtered incomings shape", len(filtered_incomings))
        for name_destination, links_destination in possible_links_mouvements.items():
            possible_destinations = gdf_matched_routes_destinations[
                gdf_matched_routes_destinations["LINK_ID"].isin(links_destination)
            ]["ANONID"].unique()
            print(f"number of possible destinations: {len(possible_destinations)}")
            gdf_sorted = gdf_matched_routes_destinations[
                gdf_matched_routes_destinations["LINK_ID"].isin(links_destination)
            ].sort_values(by="remainDistance", ascending=False)
            filtered_destinations = filtered_incomings[
                filtered_incomings["ANONID"].isin(possible_destinations)
            ]
            print(f"number of filtered destinations: {filtered_destinations.shape[0]}")
            possible_outgoings.update(gdf_sorted["ANONID"].unique())
            gdf_matched_routes_destinations = gdf_matched_routes_destinations[
                ~gdf_matched_routes_destinations["ANONID"].isin(possible_outgoings)
            ]
            if len(filtered_incomings) != 0:
                proportion = len(filtered_destinations) / len(filtered_incomings)
                print(f"propotion of outgoings to {name_destination}: {proportion}")
            else:
                proportion = 0
                print("No incomings")
            out_dict = {
                "origin": name_origin,
                "destination": name_destination,
                "proportion": proportion,
                "anonymized_ids": str(filtered_destinations["ANONID"].to_list()),
            }
            out = pd.concat([out, pd.DataFrame([out_dict])])
        return out
