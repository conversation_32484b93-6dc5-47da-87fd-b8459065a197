import json
from typing import Any

import pandas as pd
import pytest

from scripts.run_compute_proportions import execute_script, read_links_json


class TestMain:
    def test_execute_script_does_nothing_if_not_main(self) -> None:
        execute_script("__aint_no_main__")

    def test_execute_script_fails_without_args(self) -> None:
        with pytest.raises(SystemExit) as excinfo:
            execute_script("__main__")

        assert excinfo.value.code == 2

    def test_execute_script_fails_if_file_does_not_exist(self) -> None:
        args = ["non_existent_matched_routes_file", "non_existent_links_file"]

        with pytest.raises(SystemExit) as excinfo:
            execute_script("__main__", args)

        assert excinfo.value.code == 2

    def test_execute_script_computes_proportions(self, tmp_path: Any) -> None:
        matched_routes_file = tmp_path / "matched_routes.csv"
        links_file = tmp_path / "links.json"
        output_file = tmp_path / "output.csv"

        matched_routes_file.write_text(
            "ANONID,LINK_ID,remainDistance\n" "a,1,3\n" "b,1,4\n" "a,2,5\n" "b,3,6\n"
        )
        links = {
            "all_origin_links": {
                "origin1": [1],
            },
            "possible_links_mouvements": {"dest1": [2], "dest2": [3]},
        }
        with open(links_file, "w") as f:
            json.dump(links, f)

        args = [str(matched_routes_file), str(links_file), "-o", str(output_file)]
        execute_script("__main__", args)

        assert output_file.exists()
        df = pd.read_csv(output_file)
        assert not df.empty
        assert set(df["origin"]) == {"origin1"}
        assert set(df["destination"]) == {"dest1", "dest2"}
        assert set(df["proportion"]) == {0.5, 0.5}
        assert set(df["anonymized_ids"]) == {"['a']", "['b']"}

    def test_execute_script_when_origi_is_in_possible_links_mouvements(
        self, tmp_path: Any
    ) -> None:
        matched_routes_file = tmp_path / "matched_routes.csv"
        links_file = tmp_path / "links.json"
        output_file = tmp_path / "output.csv"

        matched_routes_file.write_text(
            "ANONID,LINK_ID,remainDistance\n" "a,1,3\n" "b,1,4\n" "a,2,5\n" "b,3,6\n"
        )
        links = {
            "all_origin_links": {
                "origin1": [1],
            },
            "possible_links_mouvements": {"origin1": [1], "dest1": [2], "dest2": [3]},
        }
        with open(links_file, "w") as f:
            json.dump(links, f)

        args = [str(matched_routes_file), str(links_file), "-o", str(output_file)]
        execute_script("__main__", args)

        assert output_file.exists()
        df = pd.read_csv(output_file)
        assert not df.empty
        assert set(df["origin"]) == {"origin1"}
        assert set(df["destination"]) == {"dest1", "dest2"}
        assert set(df["proportion"]) == {0.5}
        assert set(df["anonymized_ids"]) == {"['a']", "['b']"}


class TestReadLinksJson:
    def test_read_links_json_with_valid_file(self, tmp_path: Any) -> None:
        links_file = tmp_path / "links.json"
        links = {
            "all_origin_links": {
                "origin1": [1],
            },
            "possible_links_mouvements": {"dest1": [2], "dest2": [3]},
        }
        with open(links_file, "w") as f:
            json.dump(links, f)

        all_origin_links, possible_links_mouvements = read_links_json(str(links_file))

        assert all_origin_links == {"origin1": [1]}
        assert possible_links_mouvements == {"dest1": [2], "dest2": [3]}

    def test_read_links_json_with_missing_file(self) -> None:
        with pytest.raises(SystemExit) as excinfo:
            read_links_json("non_existent_links.json")

        assert excinfo.value.code == 1

    def test_read_links_json_with_invalid_file(self, tmp_path: Any) -> None:
        links_file = tmp_path / "links.json"
        invalid_json = "invalid json"

        with open(links_file, "w") as f:
            f.write(invalid_json)

        with pytest.raises(SystemExit) as excinfo:
            read_links_json(str(links_file))

        assert excinfo.value.code == 1

    def test_read_links_json_with_missing_keys(self, tmp_path: Any) -> None:
        links_file = tmp_path / "links.json"
        links = {
            "all_origin_links": {
                "origin1": [1],
            },
        }
        with open(links_file, "w") as f:
            json.dump(links, f)

        with pytest.raises(KeyError) as excinfo:
            read_links_json(str(links_file))

        assert str(excinfo.value.args[0]) == "Missing required data keys"
