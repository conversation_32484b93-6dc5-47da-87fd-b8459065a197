# -*- coding: utf-8 -*-
# import time
import re
import smtplib
import sys
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText


class MailSender:
    def __init__(self, dict_email):
        try:
            self.from_add = dict_email["sender"]
            self.smtp_server = dict_email["smtpServer"]
            self.smtp_port = dict_email["smtpPort"]
            self.password = dict_email["password"]
            self.err_code = 0
            self.err_message = ""
            if "sendSuccessReport" in dict_email.keys():
                self.send_success_report = dict_email["sendSuccessReport"]
            else:
                self.send_success_report = True
            if "sendErrorReport" in dict_email.keys():
                self.send_error_report = dict_email["sendErrorReport"]
            else:
                self.send_error_report = True
            if "cc" in dict_email.keys():
                self.cc = dict_email["cc"]
            else:
                self.cc = ""
            if "cci" in dict_email.keys():
                self.cci = dict_email["cci"]
            else:
                self.cci = ""
            if "receiver" in dict_email.keys():
                self.to_add = dict_email["receiver"]
                regex = "^[a-z0-9]+[\._]?[a-z0-9]+[@]\w+[.]\w{2,3}$"
                if not re.search(regex, dict_email["receiver"]):
                    self.err_code = 1
                    self.err_message = (
                        "Fail to initialize MailSender. Invalid syntax for receiver email "
                        + dict_email["receiver"]
                        + " (<NAME_EMAIL> or <EMAIL>)"
                    )
            else:
                self.to_add = ""
        except Exception:
            self.err_code = sys.exc_info()[1]
            self.err_message = (
                "Fail to initialize MailSender. At least one of the following keys is missing in the json configuration file:\nreceiver \nsender \nsmtpServer \nsmtpPort \npassword \ncc (optional)\ncci (optional) \nsendSuccessReport (optional, default=true) \nsendErrorReport (optional, default=true) \n"
                + str(sys.exc_info())
            )

    def send(self, subject, msg):
        if self.err_code != 0:
            self.err_message = "Fail to send email. MailSender not properly initialized"
        else:
            try:
                if self.to_add:
                    message = MIMEMultipart()  ## Création de l'objet "message"

                    message["From"] = self.from_add  ## Spécification de l'expéditeur

                    if type(self.to_add) == str:
                        message["To"] = self.to_add  ## Spécification du destinataire
                        to_adds = [self.to_add]
                    else:
                        message["To"] = ",".join(
                            self.to_add
                        )  ## Spécification des destinataires
                        to_adds = self.to_add

                    if self.cc:
                        if type(self.cc) == str:
                            message["CC"] = self.cc
                            to_adds += [self.cc]
                        else:
                            message["CC"] = ",".join(self.cc)
                            to_adds += self.cc

                    if self.cci:
                        if type(self.cci) == str:
                            message["BCC"] = self.cci
                            to_adds += [self.cci]
                        else:
                            message["BCC"] = ",".join(self.cci)
                            to_adds += self.cci

                    message["Subject"] = (
                        subject  ## Spécification de l'objet de votre mail
                    )
                    message.attach(
                        MIMEText(msg.encode("utf-8"), "plain", "utf-8")
                    )  ## Attache du message à l'objet "message", et encodage en UTF-8
                    serveur = smtplib.SMTP(
                        self.smtp_server, self.smtp_port
                    )  ## Connexion au serveur sortant (en précisant son nom et son port)
                    serveur.starttls()  ## Spécification de la sécurisation
                    serveur.login(self.from_add, self.password)  ## Authentification
                    text = message.as_string().encode(
                        "utf-8"
                    )  ## Conversion de l'objet "message" en chaine de caractère et encodage en UTF-8
                    serveur.sendmail(self.from_add, to_adds, text)  ## Envoi du mail
                    serveur.quit()  ## Déconnexion du serveur
            except Exception:
                self.err_code = sys.exc_info()[1]
                self.err_message = (
                    "Fail to send "
                    + subject
                    + " by email to "
                    + str(self.to_add)
                    + ": "
                    + str(sys.exc_info())
                )
