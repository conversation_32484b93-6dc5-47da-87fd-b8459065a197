__author__ = "Thibault Fourez"
__version__ = "1.1"
__maintainer__ = "Thibault Fourez"
__email__ = "<EMAIL>"
__status__ = "Production"
# -*- coding: utf-8 -*-

# Clés d'accès aux élément du fichier de configuration Json
KEY_YEAR = "year"
KEY_MONTH = "month"
KEY_TOKEN_URL = "tokenURL"
KEY_USE_DEMO_MODE = "useDemoMode"
KEY_CLIENT_ID = "clientID"  # Client ID à récupérer auprès de Swisscom
KEY_CLIENT_SECRET = "clientSecret"  # Mot de passe à récupérer auprès de Swisscom
KEY_ZONE_PATH = "zonePath"
KEY_NAME_FIELD_NPVM_ID = "nameFieldNPVMID"
KEY_NAME_FIELD_ZONE_ID = "nameFieldZoneID"
KEY_LIST_ZONE_INTEREST = "listZoneInterest"
KEY_LIST_ZONE_SECONDARY = "listZoneSecondary"
KEY_LIST_ZONE_SECONDARY_TRANSIT = "listZoneSecondaryTransit"
KEY_SAVE_BATCH_SIZE = (
    "saveBatchSize"  # Sauvegarde des données de sortie tous les x batchs
)
# Nombre de paires od à envoyer dans chaque requête
# (100 maximun)
KEY_REQUEST_BATCH_SIZE = "requestBatchSize"
KEY_SAVE_OUTPUT_DIR = "saveOutputDir"
# les fichiers en sortie iont un nom du type
# "<KEY_SAVE_OUTPUT_DIR>\<KEY_SAVE_NAME>_56.csv"
KEY_SAVE_NAME = "saveName"
KEY_OUTPUT_PATH = "outputPath"

# Champs de l'API de Swisscom
CONST_NAME_FIELD_PAIRS = "pairs"
CONST_NAME_FIELD_ORIGIN = "origin"
CONST_NAME_FIELD_DESTINATION = "destination"
CONST_NAME_FIELD_WEEKDAYS = "weekdays"
CONST_NAME_FIELD_WEEKEND = "weekend"
CONST_NAME_FIELD_TOTAL = "total"
CONST_NAME_FIELD_TRAIN = "train"
CONST_NAME_FIELD_OTHERS = "others"
