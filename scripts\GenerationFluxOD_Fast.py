import csv

import matplotlib.path as mpltPath
import pandas as pd
import shapefile
from shapely import geometry

probe_data_path = r"<path_to_probe_data_csv>"  # Table des traces GPS (.csv)
zone_path = r"<path_to_zone_shapefile>"  # Couche des zones (.shp).
# Les zones doivent être dans le même système de coordonnées que les traces GPS
output_od_matrix_path = (
    r"<path_to_od_matrix_output_csv"  # Chemin de la matrice OD en sortie (.csv)
)
trajectory_id_field = (
    "ANONID"  # Champ d'identification des trajets dans la table des trajets
)
latitude_field = "LATITUDE"  # Champ de latitude dans la table des trajets
longitude_field = "LONGITUDE"  # Champ de longitude dans la table des trajets
zone_id_field = "ID_zone"  # Champ d'identification des zones dans la couche des zones
origin_suffix = "_origin"
destination_suffix = "_destination"
# Retourner des proportions dans les flux OD
# (plutôt que le nombre de trajets brut)
normalize_count = False
dropna = True  # Enlever les points qui n'ont été joints à aucune zone
# Simplifier les polygones (avec une tolérance de 0.001)
# pour réduire le temps de calcul
simplify = False


def csv_to_dataframe(csv_path: str) -> pd.DataFrame:
    with open(csv_path, "r") as csvfile:
        dialect = csv.Sniffer().sniff(csvfile.readline())
        separator = dialect.delimiter
    if separator == ";":
        decimal = ","
    else:
        decimal = "."
    return pd.read_csv(csv_path, sep=separator, decimal=decimal)


def read_zone(shp_path: str, zone_id_field: str) -> dict:
    sf = shapefile.Reader(shp_path)
    shapes = sf.shapes()
    records = sf.records()
    fields = [f[0] for f in sf.fields[1:]]
    index_zone_id = fields.index(zone_id_field)
    dict_zone = {}
    for i, shape in enumerate(shapes):
        dict_zone[records[i][index_zone_id]] = shape.points
        if simplify:
            poly = geometry.Polygon(shape.points)
            poly = poly.simplify(0.001)
            dict_zone[records[i][index_zone_id]] = [
                (x, y)
                for x, y in zip(poly.exterior.coords.xy[0], poly.exterior.coords.xy[1])
            ]
    return dict_zone


def spatial_join(
    df: pd.DataFrame,
    dict_zone: dict,
    latitude_field: str,
    longitude_field: str,
    output_zone_id_field: str,
    dropna: bool = True,
) -> pd.DataFrame:
    df[output_zone_id_field] = None
    points = df[[longitude_field, latitude_field]]
    for zone_id, polygon in dict_zone.items():
        print(zone_id, len(polygon))
        path = mpltPath.Path(polygon)
        inside = path.contains_points(points)
        df.loc[inside, output_zone_id_field] = zone_id
    if dropna:
        df = df.dropna()
    return df


def generate_od_matrix(
    df: pd.DataFrame,
    trajectory_id_field: str,
    zone_id_field: str,
    origin_suffix: str = "_origin",
    destination_suffix: str = "_destination",
    normalize_count: bool = True,
) -> pd.DataFrame:
    df_origin = df.groupby(trajectory_id_field).first()
    df_destination = df.groupby(trajectory_id_field).last()
    df_od = df_origin[[zone_id_field]].join(
        df_destination[[zone_id_field]],
        lsuffix=origin_suffix,
        rsuffix=destination_suffix,
    )
    od_matrix = pd.crosstab(
        index=df_od[zone_id_field + origin_suffix],
        columns=df_od[zone_id_field + destination_suffix],
        normalize=normalize_count,
    )
    return od_matrix


def main(
    probe_data_path: str,
    zone_path: str,
    output_od_matrix_path: str,
    trajectory_id_field: str = "trajectory_id",
    latitude_field: str = "latitude",
    longitude_field: str = "longitude",
    zone_id_field: str = "zone_id",
    origin_suffix: str = "_origin",
    destination_suffix: str = "_destination",
    normalize_count: bool = True,
    dropna: bool = True,
) -> None:
    df = csv_to_dataframe(probe_data_path)
    dict_zone = read_zone(zone_path, zone_id_field)
    df = spatial_join(
        df, dict_zone, latitude_field, longitude_field, zone_id_field, dropna=dropna
    )
    od_matrix = generate_od_matrix(
        df,
        trajectory_id_field,
        zone_id_field,
        origin_suffix=origin_suffix,
        destination_suffix=destination_suffix,
        normalize_count=normalize_count,
    )
    print(od_matrix)
    od_matrix.to_csv(output_od_matrix_path)


if __name__ == "__main__":
    main(
        probe_data_path=probe_data_path,
        zone_path=zone_path,
        output_od_matrix_path=output_od_matrix_path,
        trajectory_id_field=trajectory_id_field,
        latitude_field=latitude_field,
        longitude_field=longitude_field,
        zone_id_field=zone_id_field,
        origin_suffix=origin_suffix,
        destination_suffix=destination_suffix,
        normalize_count=normalize_count,
        dropna=dropna,
    )
