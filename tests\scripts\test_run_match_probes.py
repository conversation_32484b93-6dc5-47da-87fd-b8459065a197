from pathlib import Path
from typing import Any
from unittest.mock import call, patch

import pandas as pd
import pytest

from scripts.run_match_probes import execute_script


def test_requires_apikey() -> None:
    with pytest.raises(SystemExit):
        execute_script("__main__")


@patch("scripts.run_match_probes.fetch_all_routes")
def test_should_fetch_all_routes(mock_fetch: Any, tmp_path: Path) -> None:
    output_file = tmp_path / "output.csv"
    csv_probe = tmp_path / "probe_data.csv"
    csv_probe.write_text(
        "ANONID,DATE,LATITUDE,LONGITUDE,HEADING,SPEED\n"
        "qezequef,12/05/1989,45.5,4.5,12,16\n"
        "qrl,12/06/1989,45.6,4.6,42,36\n"
    )
    url = (
        "https://routematching.hereapi.com/v8/match/routelinks"
        "?routeMatch=1&mode=fastest;car&legAttributes=mn"
        "&maneuverAttributes=ac&apikey=my_secret_key"
    )
    csv_as_gpx = [
        {
            "gpx": '<?xml version="1.0" encoding="UTF-8"?>\n'
            '<gpx xmlns="http://www.topografix.com/GPX/1/1" '
            'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" '
            'xsi:schemaLocation="http://www.topografix.com/GPX/1/1 '
            'http://www.topografix.com/GPX/1/1/gpx.xsd" version="1.1" '
            'creator="gpx.py -- https://github.com/tkrajina/gpxpy">\n  '
            '<trk>\n    <trkseg>\n      <trkpt lat="45.5" lon="4.5">\n'
            "      </trkpt>\n    </trkseg>\n  </trk>\n</gpx>",
            "ANONID": "qezequef",
            "DATE": "12/05/1989",
        },
        {
            "gpx": '<?xml version="1.0" encoding="UTF-8"?>\n<gpx '
            'xmlns="http://www.topografix.com/GPX/1/1" '
            'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" '
            'xsi:schemaLocation="http://www.topografix.com/GPX/1/1 '
            'http://www.topografix.com/GPX/1/1/gpx.xsd" version="1.1" '
            'creator="gpx.py -- https://github.com/tkrajina/gpxpy">\n  '
            '<trk>\n    <trkseg>\n      <trkpt lat="45.6" lon="4.6">\n  '
            "    </trkpt>\n    </trkseg>\n  </trk>\n</gpx>",
            "ANONID": "qrl",
            "DATE": "12/06/1989",
        },
    ]

    execute_script(
        "__main__",
        [
            "--probe-path",
            str(csv_probe),
            "--apikey",
            "my_secret_key",
            "--output-file",
            str(output_file),
        ],
    )

    mock_fetch.assert_called_with(
        csv_as_gpx, url, record_paths=["link"], routes_path=str(output_file)
    )


@patch("scripts.run_match_probes.fetch_all_routes")
def test_should_log_error(mock_fetch: Any, tmp_path: Any, caplog: Any) -> None:
    csv_probe = tmp_path / "probe_data.csv"
    csv_probe.write_text(
        "ANONID,DATE,LATITUDE,LONGITUDE,HEADING,SPEED\n"
        "qezequef,12/05/1989,45.5,4.5,12,16\n"
        "qrl,12/06/1989,45.6,4.6,42,36\n"
    )
    mock_fetch.side_effect = ValueError("coucou")

    with pytest.raises(SystemExit):
        execute_script(
            "__main__", ["--probe-path", str(csv_probe), "--apikey", "my_secret_key"]
        )

    assert "coucou" in caplog.text


@patch("scripts.run_match_probes.filter_already_matched")
@patch("scripts.run_match_probes.pd.read_csv")
def test_main_with_existing_output_file(
    mock_read_csv: Any, mock_filter_already_matched: Any, tmp_path: Path
) -> None:
    output_file = tmp_path / "output.csv"
    output_file.write_text("ANONID,LINK_ID\n" "qezequef,1214\n")
    csv_probe = tmp_path / "probe_data.csv"
    csv_probe.write_text(
        "ANONID,DATE,LATITUDE,LONGITUDE,HEADING,SPEED\n"
        "qezequef,12/05/2025,45.5,4.5,12,16\n"
        "qrl,12/06/2025,45.6,4.6,42,36\n"
    )

    execute_script(
        "__main__",
        [
            "--probe-path",
            str(csv_probe),
            "--apikey",
            "my_secret_key",
            "--output-file",
            str(output_file),
        ],
    )

    df_matched_routes = pd.read_csv(str(output_file))
    df_probes = pd.read_csv(str(csv_probe))
    mock_read_csv.assert_has_calls(
        [
            call(str(output_file)),
            call(str(csv_probe)),
        ],
        any_order=True,
    )
    mock_filter_already_matched.assert_called_with(df_matched_routes, df_probes)


@patch("scripts.run_match_probes.fetch_all_routes")
@patch("scripts.run_match_probes.filter_already_matched")
def test_main_with_non_existing_output_file(
    mock_filter_already_matched: Any, mock_fetch_all_routes: Any, tmp_path: Path
) -> None:
    output_file = tmp_path / "output.csv"
    csv_probe = tmp_path / "probe_data.csv"
    csv_probe.write_text(
        "ANONID,DATE,LATITUDE,LONGITUDE,HEADING,SPEED\n"
        "qezequef,12/05/2025,45.5,4.5,12,16\n"
        "qrl,12/06/2025,45.6,4.6,42,36\n"
    )

    execute_script(
        "__main__",
        [
            "--probe-path",
            str(csv_probe),
            "--apikey",
            "my_secret_key",
            "--output-file",
            str(output_file),
        ],
    )

    mock_filter_already_matched.assert_not_called()
    mock_fetch_all_routes.assert_called()
