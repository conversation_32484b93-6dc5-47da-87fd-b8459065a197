import argparse
import asyncio
import logging
import os
import sys
from typing import List, Optional

import pandas as pd

from workers.match_probe_routes import (
    fetch_all_routes,
    filter_already_matched,
    serialize_df_to_gpx_strings,
)


def main(args: Optional[List[str]] = None) -> None:
    parser = argparse.ArgumentParser()
    parser.add_argument("--probe-path", type=str, help="Path to the probe file")
    parser.add_argument("--apikey", type=str, help="Here API key")
    parser.add_argument(
        "--output-file",
        "-o",
        help="Path to the output file",
        default="matched_routes.csv",
    )
    parsed_args = parser.parse_args(args)
    probe_path = parsed_args.probe_path
    df_probes = pd.read_csv(probe_path)
    if os.path.exists(parsed_args.output_file):
        df_matched_routes = pd.read_csv(parsed_args.output_file)
        df_probes = filter_already_matched(df_matched_routes, df_probes)
    df_gpx = serialize_df_to_gpx_strings(df_probes)
    apikey = parsed_args.apikey
    url = (
        f"https://routematching.hereapi.com/v8/match/routelinks"
        f"?routeMatch=1&mode=fastest;car&legAttributes=mn"
        f"&maneuverAttributes=ac&apikey={apikey}"
    )
    loop = asyncio.get_event_loop()
    loop.run_until_complete(
        fetch_all_routes(
            df_gpx, url, record_paths=["link"], routes_path=parsed_args.output_file
        )
    )


def execute_script(name: str, args: Optional[List[str]] = None) -> None:
    if name == "__main__":
        try:
            main(args)
        except Exception as e:
            logging.error(e)
            sys.exit(2)


execute_script(__name__)
