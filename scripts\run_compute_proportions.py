import argparse
import json
import logging
import os
import sys
from typing import Dict, List, Optional, Tuple

import geopandas as gpd

from workers.compute_proportions import RouteProportions


def execute(
    gdf_matched_routes: gpd.GeoDataFrame,
    all_origin_links: Dict[str, List[int]],
    possible_links_mouvements: Dict[str, List[int]],
    output_file: str,
) -> None:
    rp = RouteProportions(gdf_matched_routes)
    for i, (name_origin, links_origin) in enumerate(all_origin_links.items()):
        print(f"Computing for {name_origin}")
        print("-------------------")
        links_movements = {
            o: d for o, d in possible_links_mouvements.items() if o != name_origin
        }

        counts = rp.compute_outgoing_proportions(
            links_origin, links_movements, name_origin
        )
        print("-------------------")
        counts.to_csv(
            output_file,
            index=False,
            mode="a",
            header=not os.path.exists(output_file),
        )


def parse_args(args: Optional[List[str]]) -> argparse.Namespace:
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "matched_routes",
        metavar="matched_routes.csv",
        help="Path to the matched routes file",
    )
    parser.add_argument(
        "links_file",
        metavar="links.json",
        help="Path to the links file",
        default="links.json",
    )
    parser.add_argument(
        "--output-file",
        "-o",
        help="Path to the output file",
        default="proportions.csv",
    )
    return parser.parse_args(args)


def read_links_json(
    filename: str,
    origin_links_key: str = "all_origin_links",
    possible_links_key: str = "possible_links_mouvements",
) -> Tuple[Dict[str, List[int]], Dict[str, List[int]]]:
    try:
        with open(filename, "r") as f:
            data = json.load(f)
    except FileNotFoundError:
        logging.error(f"File {filename} not found")
        sys.exit(1)
    except json.JSONDecodeError as e:
        logging.error(f"Error reading file {filename}:\n{e}")
        sys.exit(1)
    if origin_links_key not in data or possible_links_key not in data:
        raise KeyError("Missing required data keys")

    all_origin_links = data[origin_links_key]
    possible_links_mouvements = data[possible_links_key]
    return all_origin_links, possible_links_mouvements


def main(args: Optional[List[str]]) -> None:
    parsed_args = parse_args(args)
    gdf_matched_routes = gpd.read_file(parsed_args.matched_routes)
    all_origin_links, possible_links_mouvements = read_links_json(
        parsed_args.links_file
    )
    execute(
        gdf_matched_routes,
        all_origin_links,
        possible_links_mouvements,
        parsed_args.output_file,
    )


def execute_script(name: str, args: Optional[List[str]] = None) -> None:
    if name == "__main__":
        try:
            main(args)
        except Exception as e:
            logging.error(e)
            sys.exit(2)


execute_script(__name__)
