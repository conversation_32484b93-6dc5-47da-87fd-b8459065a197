from typing import Any

import geopandas as gpd
import pandas as pd
import pytest

from workers.compute_proportions import RouteProportions


@pytest.fixture()
def route_proportions() -> Any:
    default_anon_ids = ["a", "a", "b", "b", "b", "c"]
    default_links = [1, 1, 1, 2, 2, 2]
    default_remain_distances = [3, 2, 4, 3, 2, 2]

    columns = ["ANONID", "LINK_ID", "remainDistance"]
    data = [
        arg
        for arg in [default_anon_ids, default_links, default_remain_distances]
        if arg is not None
    ]
    default_gdf = gpd.GeoDataFrame(dict(zip(columns, data)))

    def _route_proportions_factory(
        gdf: gpd.GeoDataFrame = default_gdf,
    ) -> RouteProportions:
        return RouteProportions(gdf)

    return _route_proportions_factory


class TestComputeProportions:
    def test_check_matched_routes(self, route_proportions: Any) -> None:
        anon_ids = ["a", "a", "b", "b", "b", "c"]
        links = [1, 1, 1, 2, 2, 2]
        remain_distances = [3, 2, 4, 3, 2, 2]
        gdf = gpd.GeoDataFrame(
            {
                "ANONID": anon_ids,
                "LINK_ID": links,
                "remainDistance": remain_distances,
            }
        )

        result = route_proportions(gdf)

        assert result.gdf_matched_routes.equals(gdf)

    def test_check_matched_routes_link_id_not_int(self, route_proportions: Any) -> None:
        anon_ids = ["a", "a", "b", "b", "b", "c"]
        links = ["a", "1", "1", "2", "2", "2"]
        remain_distances = [3, 2, 4, 3, 2, 2]
        gdf = gpd.GeoDataFrame(
            {
                "ANONID": anon_ids,
                "LINK_ID": links,
                "remainDistance": remain_distances,
            }
        )

        with pytest.raises(ValueError) as excinfo:
            route_proportions(gdf)

        assert str(excinfo.value) == "The column LINK_ID should contain only integers."

    def test_check_matched_routes_link_id_is_int(self, route_proportions: Any) -> None:
        anon_ids = ["a", "a", "b", "b", "b", "c"]
        links = [1, 1, 1, 2, 2, 2]
        remain_distances = [3, 2, 4, 3, 2, 2]
        gdf = gpd.GeoDataFrame(
            {
                "ANONID": anon_ids,
                "LINK_ID": links,
                "remainDistance": remain_distances,
            }
        )

        result = route_proportions(gdf)

        assert result.gdf_matched_routes.equals(gdf)
        assert result.gdf_matched_routes["LINK_ID"].dtype == "int64"

    def test_check_matched_routes_missing_column(self, route_proportions: Any) -> None:
        anon_ids = ["a", "a"]
        links = [1, 1]

        gdf = gpd.GeoDataFrame(
            {
                "ANONID": anon_ids,
                "LINK_ID": links,
            }
        )

        with pytest.raises(ValueError) as excinfo:
            route_proportions(gdf)

        assert str(excinfo.value) == (
            "The input dataframe is expected to have at least columns "
            "['LINK_ID', 'ANONID', 'remainDistance']."
        )

    def test_check_matched_routes_null_values(self, route_proportions: Any) -> None:
        anon_ids = ["a", None]
        links = [1, 1]
        remain_distances = [3, 2]
        gdf = gpd.GeoDataFrame(
            {
                "ANONID": anon_ids,
                "LINK_ID": links,
                "remainDistance": remain_distances,
            }
        )

        with pytest.raises(ValueError) as excinfo:
            route_proportions(gdf)

        assert str(excinfo.value) == "The column ANONID should not contain null values."

    def test_compute_incomings_df(self, route_proportions: Any) -> None:

        result = route_proportions().compute_incomings_df([1])
        expected_df = pd.DataFrame(
            {"ANONID": ["a", "b"], "LINK_ID": [1, 1], "remainDistance": [3, 4]}
        )

        assert len(result) == 2
        assert set(result["ANONID"]) == {"a", "b"}
        pd.testing.assert_frame_equal(result, expected_df)

    def test_compute_possible_outgoings(self, route_proportions: Any) -> None:

        result = route_proportions().compute_possible_outgoings({"test": [1]})

        assert result == {"a", "b"}

    def test_filter_incomings_without_mouvements(self, route_proportions: Any) -> None:
        result = route_proportions().filter_incomings_without_mouvements(
            [1, 2], {"test": [1, 2]}
        )
        expected_df = pd.DataFrame(
            {
                "ANONID": ["a", "b", "c"],
                "LINK_ID": [1, 1, 2],
                "remainDistance": [3, 4, 2],
            }
        )
        assert len(result) == 3
        assert set(result["ANONID"]) == {"a", "b", "c"}
        pd.testing.assert_frame_equal(result, expected_df)

    def test_compute_outgoing_proportions(self, route_proportions: Any) -> None:

        result = route_proportions().compute_outgoing_proportions(
            [1, 2], {"test": [1, 2]}, "test_origin"
        )
        expected_df = pd.DataFrame(
            {
                "origin": ["test_origin"],
                "destination": ["test"],
                "proportion": [1.0],
                "anonymized_ids": ["['a', 'b', 'c']"],
            }
        )

        assert len(result) == 1
        assert result.iloc[0]["origin"] == "test_origin"
        assert result.iloc[0]["destination"] == "test"
        assert result.iloc[0]["proportion"] == 1.0
        assert result.iloc[0]["anonymized_ids"] == "['a', 'b', 'c']"
        pd.testing.assert_frame_equal(result, expected_df)

    def test_compute_outgoing_proportions_multiple_destinations(
        self, route_proportions: Any
    ) -> None:
        anon_ids = ["a", "b", "a", "b"]
        links = [1, 1, 2, 3]
        remain_distances = [3, 2, 3, 0]

        gdf = gpd.GeoDataFrame(
            {
                "ANONID": anon_ids,
                "LINK_ID": links,
                "remainDistance": remain_distances,
            }
        )
        rp = route_proportions(gdf)

        result = rp.compute_outgoing_proportions(
            [1], {"test1": [2], "test2": [3]}, "test_origin"
        )
        result = result.reset_index(drop=True)
        expected_df = pd.DataFrame(
            {
                "origin": ["test_origin", "test_origin"],
                "destination": ["test1", "test2"],
                "proportion": [0.5, 0.5],
                "anonymized_ids": ["['a']", "['b']"],
            }
        )

        assert len(result) == 2
        assert set(result["origin"]) == {"test_origin"}
        assert set(result["destination"]) == {"test1", "test2"}
        pd.testing.assert_frame_equal(result, expected_df)

    def test_compute_outgoing_proportions_single_movement(
        self, route_proportions: Any
    ) -> None:
        anon_ids = ["a", "a", "b", "b", "b", "b", "b"]
        links = [1, 2, 1, 2, 1, 4, 5]
        remain_distances = [3, 2, 1, 2, 3, 7, 6]

        gdf = gpd.GeoDataFrame(
            {
                "ANONID": anon_ids,
                "LINK_ID": links,
                "remainDistance": remain_distances,
            }
        )
        rp = route_proportions(gdf)

        result = rp.compute_outgoing_proportions(
            [1], {"test1": [2, 3], "test2": [4, 5, 6]}, "test_origin"
        )
        result = result.reset_index(drop=True)
        expected_df = pd.DataFrame(
            {
                "origin": ["test_origin", "test_origin"],
                "destination": ["test1", "test2"],
                "proportion": [1.0, 0.0],
                "anonymized_ids": ["['a', 'b']", "[]"],
            }
        )

        assert set(result["origin"]) == {"test_origin"}
        assert set(result["destination"]) == {"test1", "test2"}
        pd.testing.assert_frame_equal(result, expected_df)

    def test_compute_outgoing_proportions_no_incomings(
        self, route_proportions: Any
    ) -> None:

        result = route_proportions().compute_outgoing_proportions(
            [3], {"test": [1, 2]}, "test"
        )
        expected_df = pd.DataFrame(
            {
                "origin": ["test"],
                "destination": ["test"],
                "proportion": [0],
                "anonymized_ids": ["[]"],
            }
        )

        pd.testing.assert_frame_equal(result, expected_df)
