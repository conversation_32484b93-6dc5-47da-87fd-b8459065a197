import asyncio
from typing import Any
from unittest.mock import call, patch

import aiohttp
import gpxpy
import pandas as pd
import pytest
from aioresponses import aioresponses

from workers.match_probe_routes import (
    convert_df_to_gpx_string,
    fetch,
    fetch_all_routes,
    filter_already_matched,
    normalize_leg_data,
    process_batch_legs,
    serialize_df_to_gpx_strings,
)


def test_normalize_leg_data() -> None:
    leg_dict = {
        "link": [
            {
                "linkId": "string",
                "length": 0.1,
                "remainDistance": 0.1,
                "remainTime": 0.1,
                "shape": [],
                "functionalClass": 0,
            }
        ],
        "length": 0,
        "travelTime": 3800,
        "refReplacements": {},
    }
    record_path = "link"
    expected_df = pd.DataFrame(
        {
            "linkId": ["string"],
            "length": [0.1],
            "remainDistance": [0.1],
            "remainTime": [0.1],
            "shape": [[]],
            "functionalClass": [0],
            "leg_length": [0],
            "leg_travelTime": [3800],
            "leg_refReplacements": [{}],
        }
    )
    print(expected_df.dtypes)
    result_df = normalize_leg_data(leg_dict, record_path)
    print(f"result_df: {result_df.dtypes}")

    pd.testing.assert_frame_equal(result_df, expected_df)


def test_normalize_leg_data_raises_keyerror() -> None:
    leg_dict = {
        "length": 0,
        "travelTime": 3800,
        "link": [],
        "maneuver": [],
        "shape": [],
        "trafficTime": 0,
        "refReplacements": {},
    }
    record_path = "nonexistent_path"

    with pytest.raises(KeyError):
        normalize_leg_data(leg_dict, record_path)


def test_process_batch_legs() -> None:
    leg_dict = {
        "leg": [
            {
                "link": [
                    {
                        "linkId": "string",
                        "length": 0.1,
                        "remainDistance": 0.1,
                        "remainTime": 0.1,
                        "shape": [],
                        "functionalClass": 0,
                    }
                ],
                "length": 0,
                "travelTime": 3800,
                "refReplacements": {},
            }
        ],
    }
    payload = {"ANONID": "test_anonid", "DATE": "12/12/2020"}
    record_paths = ["link"]
    expected_df = pd.DataFrame(
        {
            "linkId": ["string"],
            "length": [0.1],
            "remainDistance": [0.1],
            "remainTime": [0.1],
            "shape": [[]],
            "functionalClass": [0],
            "leg_length": [0],
            "leg_travelTime": [3800],
            "leg_refReplacements": [{}],
            "ANONID": ["test_anonid"],
            "DATE": ["12/12/2020"],
        }
    )

    result_df = process_batch_legs(leg_dict, payload, record_paths)

    pd.testing.assert_frame_equal(result_df, expected_df)


def test_convert_df_to_gpx_string() -> None:
    df = pd.DataFrame(
        {
            "LATITUDE": [52.5200, 48.8566, 37.7749],
            "LONGITUDE": [13.4050, 2.3522, -122.4194],
        }
    )

    expected_gpx_string = """<?xml version="1.0" encoding="UTF-8"?>
    <gpx xmlns="http://www.topografix.com/GPX/1/1" version="1.1"
     creator="gpx.py -- https://github.com/tkrajina/gpxpy">
    <trk>
        <trkseg>
        <trkpt lat="52.52" lon="13.405"></trkpt>
        <trkpt lat="48.8566" lon="2.3522"></trkpt>
        <trkpt lat="37.7749" lon="-122.4194"></trkpt>
        </trkseg>
    </trk>
    </gpx>
    """

    result_gpx_string = convert_df_to_gpx_string(df)

    expected_gpx = gpxpy.parse(expected_gpx_string)
    result_gpx = gpxpy.parse(result_gpx_string)

    assert str(expected_gpx) == str(result_gpx)


@patch("workers.match_probe_routes.convert_df_to_gpx_string")
def test_serialize_df_to_gpx_strings(mock_convert_df_to_gpx_string: Any) -> None:
    df = pd.DataFrame(
        {
            "ANONID": ["test_anonid", "test_anonid2"],
            "DATE": ["16/05/2024", "16/05/2025"],
            "LATITUDE": [52, 31],
            "LONGITUDE": [10, -10],
        }
    )
    mock_convert_df_to_gpx_string.return_value = "mock_gpx_string"

    result = serialize_df_to_gpx_strings(df)
    print(result)

    expected_result = [
        {"ANONID": "test_anonid", "DATE": "16/05/2024", "gpx": "mock_gpx_string"},
        {"ANONID": "test_anonid2", "DATE": "16/05/2025", "gpx": "mock_gpx_string"},
    ]
    assert result == expected_result


def test_fetch_returns_expected_response() -> None:
    loop = asyncio.get_event_loop()
    session = aiohttp.ClientSession()
    url = "http://example.com"
    payload = "payload"
    headers = {"Content-Type": "application/binary"}
    with aioresponses() as m:
        m.post(
            url,
            status=200,
            payload={"coucou": "les copains"},
        )

        response = loop.run_until_complete(fetch(session, url, payload, headers))

        expected_result = {"coucou": "les copains"}

        assert response == expected_result


def test_fetch_times_out() -> None:
    loop = asyncio.get_event_loop()
    session = aiohttp.ClientSession()
    url = "http://example.com"
    payload = "payload"
    headers = {"Content-Type": "application/binary"}
    with aioresponses() as m:
        m.post(
            url,
            status=503,
            exception=asyncio.TimeoutError(1),
        )

        with pytest.raises(asyncio.exceptions.TimeoutError):
            loop.run_until_complete(fetch(session, url, payload, headers))


@patch("workers.match_probe_routes.process_batch_legs")
@patch("workers.match_probe_routes.print")
@patch("workers.match_probe_routes.fetch")
def test_fetch_all_routes_should_raise_error(
    mock_fetch: Any, mock_print: Any, mock_process_batch_legs: Any, tmp_path: Any
) -> None:
    loop = asyncio.get_event_loop()
    gpx_strings = [
        {"gpx": "gpx_string_1", "ANONID": "test_anonid_1", "DATE": "16/05/2024"},
        {"gpx": "gpx_string_2", "ANONID": "test_anonid_2", "DATE": "16/05/2025"},
    ]
    url = "http://example.com"
    record_paths = ["maneuver", "link"]
    batch_size = 100
    routes_path = str(tmp_path / "legMatched.csv")

    mock_fetch.side_effect = [
        {"response": {"route": [{"leg": [{"link": []}]}]}},
        {"response": {"route": [{"leg": [{"link": []}]}]}},
    ]
    mock_process_batch_legs.side_effect = KeyError("Error")

    result = loop.run_until_complete(
        fetch_all_routes(gpx_strings, url, record_paths, batch_size, routes_path)
    )

    assert isinstance(result, pd.DataFrame)
    assert len(result) == 0
    mock_print.assert_has_calls([call(str(KeyError("Error")))])


@patch("workers.match_probe_routes.process_batch_legs")
@patch("workers.match_probe_routes.print")
@patch("workers.match_probe_routes.fetch")
def test_fetch_all_routes_should_concat_returned_values(
    mock_fetch: Any, mock_print: Any, mock_process_batch_legs: Any, tmp_path: Any
) -> None:
    loop = asyncio.get_event_loop()
    gpx_strings = [
        {"gpx": "gpx_string_1", "ANONID": "test_anonid_1", "DATE": "16/05/2024"},
        {"gpx": "gpx_string_2", "ANONID": "test_anonid_2", "DATE": "16/05/2025"},
    ]
    url = "http://example.com"
    record_paths = ["maneuver", "link"]
    batch_size = 100
    routes_path = str(tmp_path / "legMatched.csv")
    mock_fetch.side_effect = [
        {"response": {"route": [{"leg": [{"link": []}]}]}},
        {"response": {"route": [{"leg": [{"link": []}]}]}},
    ]
    mock_process_batch_legs.side_effect = [
        pd.DataFrame(
            {
                "ANONID": ["oefuhozefuh", "iefjozif"],
                "linkId": ["1", "2"],
                "DATE": ["16/05/2024", "16/05/2025"],
            }
        ),
        pd.DataFrame(
            {
                "ANONID": ["qezofquezof"],
                "linkId": ["5461"],
                "DATE": ["16/06/2024"],
            }
        ),
    ]

    result = loop.run_until_complete(
        fetch_all_routes(gpx_strings, url, record_paths, batch_size, routes_path)
    )

    expected_result = pd.DataFrame(
        {
            "ANONID": ["oefuhozefuh", "iefjozif", "qezofquezof"],
            "linkId": ["1", "2", "5461"],
            "DATE": ["16/05/2024", "16/05/2025", "16/06/2024"],
            "LINK_ID": [1, 2, 5461],
        }
    )
    assert isinstance(result, pd.DataFrame)
    pd.testing.assert_frame_equal(result, expected_result)


@patch("workers.match_probe_routes.print")
def test_filter_already_matched(mock_print: Any) -> None:
    gdf_matched_routes = pd.DataFrame(
        {
            "ANONID": ["qezofquezof"],
            "linkId": ["5461"],
            "DATE": ["16/06/2024"],
        }
    )
    gdf_probes = pd.DataFrame(
        {
            "ANONID": ["qezofquezof", "not_matched"],
            "linkId": ["5461", "0000"],
            "DATE": ["16/06/2024", "16/06/2024"],
        }
    )
    result = filter_already_matched(gdf_matched_routes, gdf_probes)

    expected_result = pd.DataFrame(
        {
            "ANONID": ["not_matched"],
            "linkId": ["0000"],
            "DATE": ["16/06/2024"],
        }
    )
    assert isinstance(result, pd.DataFrame)
    mock_print.assert_called_once_with("Probes already matched: 1 out of 2")
    pd.testing.assert_frame_equal(result, expected_result)
