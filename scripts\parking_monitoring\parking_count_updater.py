import json
import os
import sys
import time
from dataclasses import dataclass
from datetime import datetime as dt
from datetime import <PERSON><PERSON><PERSON>
from typing import Callable, Dict, List, TypeVar

import constants
import Mail<PERSON><PERSON>
from arcgis.features import FeatureLayer
from arcgis.gis import GIS
from loggers import get_logger
from retry.api import retry_call

T = TypeVar("T")

# Global variables
json_pathname = ""
json_file = ""

logger = get_logger()


class TaskError(Exception):
    def __init__(self, code):
        self.code = code

    def __str__(self):
        return repr(self.code)


def load_json():
    try:
        if len(sys.argv) >= constants.SYSTEM_ARGUMENT_LENGTH:
            json_pathname = sys.argv[1]
        else:
            error_msg = (
                f"Code Error: {constants.CODE_ERR_EXECUTION_PARAM_MISSING} "
                "Missing parameter, please set the parameters like: "
                "<requestJsonConfigurationFile>"
            )
            logger.error(error_msg)
            logger.info("End of program")
            sys.exit(constants.CODE_ERR_EXECUTION_PARAM_MISSING)

        with open(json_pathname, encoding="utf-8") as json_data:
            input_file = json.load(json_data)
        return json_pathname, input_file

    except Exception:
        if not os.path.exists(json_pathname):
            error_msg = (
                f"Code Error: {constants.CODE_ERR_JSON_PATH}, "
                f"config file: {json_pathname} doesn't exist"
            )
            logger.error(error_msg)
            logger.info("End of program")
            sys.exit(constants.CODE_ERR_JSON_PATH)
        else:
            error_msg = (
                f"Code Error: {constants.CODE_ERR_SYNTAX_INVALID}, "
                f"invalid syntax in Json file: {json_pathname}, please verify"
            )
            logger.error(error_msg)
            logger.info("End of program")
            sys.exit(constants.CODE_ERR_SYNTAX_INVALID)


def email_program_error(input_file, err_code, mail_sender, error_message=""):
    """Send email notification for program errors."""
    try:
        request_name = input_file.get("requestName", "")
        current_time = time.time()
        current_time_format = time.strftime(
            "%Y-%m-%d %H:%M:%S", time.localtime(current_time)
        )

        contents = (
            (f"Request Name: {request_name}\n" if request_name else "")
            + f"Config File: {json_pathname}\n"
            + f"Status: {constants.REQUEST_STATUS_FAILED_TO_EXECUTE}\n"
            + f"Error: {err_code}"
            + (f" - {error_message}" if error_message else "")
            + f"\n\nSubmitted Request Time: {current_time_format}"
        )

        mail_sender.send(
            f"{constants.MAIL_SUBJECT_PROGRAM_ERROR} - "
            f"{os.path.basename(json_pathname)}",
            contents,
        )

        if mail_sender.err_code != 0:
            logger.warning(mail_sender.err_message)
            error_msg = (
                f"Code Error: {constants.CODE_ERR_EMAIL_PROGRAM_ERROR} "
                f"{mail_sender.err_message}"
            )
            logger.error(error_msg)
            raise TaskError(constants.CODE_ERR_EMAIL_PROGRAM_ERROR)
        elif mail_sender.to_add:
            logger.info(constants.LOGGING_EMAIL_SENT)

    except Exception:
        e = sys.exc_info()[1]
        error_msg = (
            f"Code Error: {constants.CODE_ERR_EMAIL_PROGRAM_ERROR} "
            f"{e.args[0]} {constants.LOGGING_EMAIL_PROGRAM_ERROR}"
        )
        logger.error(error_msg)
        raise TaskError(constants.CODE_ERR_EMAIL_PROGRAM_ERROR)


@dataclass
class LevelRatios:
    """Configuration for parking level ratios."""

    lowRatio: float = 0.25
    mediumRatio: float = 0.5
    highRatio: float = 0.75


class UpdateFeatureLayerClass:
    """Class to update feature layer occupancy data."""

    def __init__(self, agol_dict, update_FL_dict):
        self.agol_dict = agol_dict
        self.update_FL_dict = update_FL_dict
        self.warnings = 0
        self.errors = 0

    def auto_retry(self, execute_query: Callable[[], T], tries: int = 5) -> T:
        return retry_call(execute_query, tries=tries, jitter=1)

    def connection_arcgis(self):
        """Connect to ArcGIS Online."""
        try:
            profile = self.agol_dict[constants.AGOL_KEY_USERNAME]
            password = self.agol_dict[constants.AGOL_KEY_PWD]
            website = self.agol_dict[constants.AGOL_KEY_URL]

            gis = GIS(website, profile, password)
            login_msg = f"Logged in as: {gis.properties.user.username}"
            logger.info(login_msg)
            self.gis = gis

        except Exception:
            e = sys.exc_info()[1]
            error_msg = (
                f"Code Error: {constants.CODE_ERR_AGOL_CONNEXION_ERROR} "
                f"Fail to connect to arcgis online: {e.args[0]}"
            )
            logger.error(error_msg)
            raise TaskError(constants.CODE_ERR_AGOL_CONNEXION_ERROR)

    def update_occupancy(
        self,
        input_layer_url: str,
        output_layer_url: str,
        enquete_fields: List[str] = None,
        date_selected: str = None,
        level_ratios: Dict[str, float] = None,
    ):
        """Update occupancy data in the feature layer."""
        try:
            # Connect to AGOL
            self.auto_retry(self.connection_arcgis)

            enquete_fields = enquete_fields or [
                "Parking",
                "Poids",
                "Date_",
                "Mode",
                "TauxOccupation",
            ]
            level_ratios = LevelRatios(**(level_ratios or {}))

            query = "TauxOccupation is not null "
            layer = FeatureLayer(input_layer_url)

            # Get all rows that match the query
            query_msg = f"Executing query: {query}"
            logger.info(query_msg)

            filtered_rows = self.auto_retry(
                lambda: layer.query(where=query, out_fields=enquete_fields).features
            )

            success_msg = (
                f"Query executed successfully. "
                f"Number of rows fetched: {len(filtered_rows)}"
            )
            logger.info(success_msg)

            # Calculate time window
            now = dt.now()
            if now.hour < 5:
                datetime_from = (now - timedelta(hours=24)).replace(
                    hour=5, minute=0, second=0
                )
                datetime_to = now.replace(hour=5, minute=0, second=0)
            else:
                datetime_from = now.replace(hour=5, minute=0, second=0)
                datetime_to = (now + timedelta(hours=24)).replace(
                    hour=5, minute=0, second=0
                )

            # Group data by parking spot
            grouped_data = {}
            for row in filtered_rows:
                enqueteur = row.attributes["Parking"]
                poids = row.attributes["Poids"] or 0
                date = row.attributes["Date_"]
                date_record = dt.fromtimestamp(date / 1000)

                if datetime_from <= date_record < datetime_to:
                    if enqueteur in grouped_data:
                        grouped_data[enqueteur] += poids
                    else:
                        grouped_data[enqueteur] = poids

            counting_msg = f"Counting spots to update: {grouped_data.keys()}"
            logger.info(counting_msg)

            # Update output layer
            output_layer = FeatureLayer(output_layer_url)
            capacity_rows = self.auto_retry(lambda: output_layer.query().features)
            capacity_dict_features = {
                row.attributes["CountingSpots"]: row for row in capacity_rows
            }

            if not grouped_data:
                # No data - set all to 0
                for feature in capacity_rows:
                    feature.attributes["Occupancy"] = 0
                    feature.attributes["level_category"] = 1
                result = self.auto_retry(
                    lambda: output_layer.edit_features(updates=capacity_rows)
                )
            else:
                # Update with actual data
                updates = []
                for counting_spots, occupancy in grouped_data.items():
                    try:
                        feature = capacity_dict_features.get(counting_spots)
                        if not feature:
                            continue

                        capacity = feature.attributes["Capacity"]
                        if not capacity or capacity <= 0 or occupancy <= 0:
                            level_category = 0
                        else:
                            ratio = occupancy / capacity
                            if ratio <= level_ratios.lowRatio:
                                level_category = 1
                            elif ratio <= level_ratios.mediumRatio:
                                level_category = 2
                            elif ratio <= level_ratios.highRatio:
                                level_category = 3
                            else:
                                level_category = 4

                        feature.attributes["Occupancy"] = occupancy
                        feature.attributes["level_category"] = level_category
                        updates.append(feature)

                    except Exception:
                        error_msg = (
                            f"Error: The counting spot {counting_spots} "
                            "does not match any feature in the output layer"
                        )
                        logger.warning(error_msg)
                        continue

                result = self.auto_retry(
                    lambda: output_layer.edit_features(updates=updates)
                )

            return result

        except Exception:
            e = sys.exc_info()[1]
            error_msg = (
                f"Code Error: {constants.CODE_ERR_UPDATEFL_ERROR} "
                f"Fail to update existing features. {e.args[0]}"
            )
            logger.error(error_msg)

            # Log request parameters that failed
            params_msg = f"Request parameters: {input_layer_url} {output_layer_url}"
            logger.error(params_msg)

            raise TaskError(constants.CODE_ERR_UPDATEFL_ERROR)


def main():
    try:
        global json_pathname, json_file
        # Start
        logger.info(constants.LOGGING_START)

        # Load JSON configuration
        json_pathname, json_file = load_json()

        # Load MailSender
        if constants.EMAIL_DICT in json_file.keys():
            mail_sender = MailSender.MailSender(json_file[constants.EMAIL_DICT])
            if mail_sender.err_code != 0:
                error_msg = (
                    f"Code Error: {constants.CODE_ERR_EMAIL_PROGRAM_ERROR} "
                    f"{mail_sender.err_message}"
                )
                logger.error(error_msg)
                sys.exit(constants.CODE_ERR_EMAIL_PROGRAM_ERROR)
        else:
            error_msg = (
                f"Code Error: {constants.CODE_ERR_DICT_MISSING} "
                "email dict not in json configuration file"
            )
            logger.error(error_msg)
            sys.exit(constants.CODE_ERR_DICT_MISSING)

        # Check if Global parameters exist
        if constants.GLOBALPARAMETERS_DICT not in json_file.keys():
            error_msg = (
                f"Code Error: {constants.CODE_ERR_DICT_MISSING} "
                "globalParameters dict not in json configuration file"
            )
            logger.error(error_msg)
            sys.exit(constants.CODE_ERR_DICT_MISSING)

        # Process AGOL updates
        if constants.AGOL_DICT in json_file.keys():
            counting_spots_layer = json_file[constants.LAYERURL_KEY_INPUTLAYER]
            capacity_layer = json_file[constants.LAYERURL_KEY_UPDATELAYER]

            execution = UpdateFeatureLayerClass(
                json_file[constants.AGOL_DICT],
                json_file[constants.UPDATEFL_DICT],
            )

            # Update the occupancy data
            execution.update_occupancy(
                counting_spots_layer,
                capacity_layer,
                level_ratios=json_file[constants.LEVELRATIOS_DICT],
            )

        logger.info(constants.LOGGING_FINISH)

    except IndexError:
        error_msg = (
            f"Code Error: {constants.CODE_ERR_INDEX_ERR} "
            "Process finished with index error"
        )
        logger.error(error_msg)

        if "mail_sender" in locals():
            email_program_error(
                json_file,
                constants.CODE_ERR_INDEX_ERR,
                mail_sender,
                "Process finished with index error",
            )
        logger.info(constants.LOGGING_FINISH)
        sys.exit(int(constants.CODE_ERR_INDEX_ERR))

    except TypeError:
        error_msg = (
            f"Code Error: {constants.CODE_ERR_TYPE_ERR} "
            "Process finished with type error"
        )
        logger.error(error_msg)

        if "mail_sender" in locals():
            email_program_error(
                json_file,
                constants.CODE_ERR_TYPE_ERR,
                mail_sender,
                "Process finished with type error",
            )
        logger.info(constants.LOGGING_FINISH)
        sys.exit(int(constants.CODE_ERR_TYPE_ERR))

    except NameError:
        error_msg = (
            f"Code Error: {constants.CODE_ERR_NAME_ERR} "
            "Process finished with name error"
        )
        logger.error(error_msg)

        if "mail_sender" in locals():
            email_program_error(
                json_file,
                constants.CODE_ERR_NAME_ERR,
                mail_sender,
                "Process finished with name error",
            )
        logger.info(constants.LOGGING_FINISH)
        sys.exit(int(constants.CODE_ERR_NAME_ERR))

    except OSError:
        error_msg = "Process finished with OS error"
        logger.error(error_msg)

        if "mail_sender" in locals():
            email_program_error(
                json_file,
                "OS error",
                mail_sender,
                "Process finished with OS error",
            )
        logger.info(constants.LOGGING_FINISH)
        sys.exit(int(str(sys.exc_info()[1])))

    except ValueError:
        error_msg = (
            f"Code Error: {constants.CODE_ERR_VALUE_ERR} "
            "Process finished with value error"
        )
        logger.error(error_msg)

        if "mail_sender" in locals():
            email_program_error(
                json_file,
                constants.CODE_ERR_VALUE_ERR,
                mail_sender,
                "Process finished with value error",
            )
        logger.info(constants.LOGGING_FINISH)
        sys.exit(int(constants.CODE_ERR_VALUE_ERR))

    except KeyError:
        error_msg = "Process finished with key error"
        logger.error(error_msg)

        if "mail_sender" in locals():
            email_program_error(
                json_file,
                "Key error",
                mail_sender,
                "Process finished with key error",
            )
        logger.info(constants.LOGGING_FINISH)
        sys.exit(int(str(sys.exc_info()[1])))

    except ModuleNotFoundError:
        error_msg = (
            f"Code Error: {constants.CODE_ERR_PACKAGE_MISSING} "
            f"Process finished with module not found error: {sys.exc_info()}"
        )
        logger.error(error_msg)

        if "mail_sender" in locals():
            email_program_error(
                json_file,
                constants.CODE_ERR_PACKAGE_MISSING,
                mail_sender,
                f"Process finished with module not found error: {sys.exc_info()}",
            )
        logger.info(constants.LOGGING_FINISH)
        sys.exit(int(constants.CODE_ERR_PACKAGE_MISSING))

    except TaskError as e:
        error_msg = f"Code Error: {e.code} Process finished task error"
        logger.error(error_msg)

        if "mail_sender" in locals():
            email_program_error(
                json_file, e.code, mail_sender, "Process finished task error"
            )
        logger.info(constants.LOGGING_FINISH)
        sys.exit(e.code)


if __name__ == "__main__":
    main()
