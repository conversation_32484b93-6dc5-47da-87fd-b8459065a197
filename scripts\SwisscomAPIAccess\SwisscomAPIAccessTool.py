__author__ = "Thiba<PERSON> Fourez"
__version__ = "1.1"
__maintainer__ = "Thibault Fourez"
__email__ = "<EMAIL>"
__status__ = "Production"
# -*- coding: utf-8 -*-

import os
import sys
from datetime import datetime as dt
from typing import List

import numpy as np
import pandas as pd
import SwisscomAPIAccessConst as const  # constantes de l'outil
import tool  # classe abstraite dont hérite la classe de l'outil
from oauthlib.oauth2 import BackendApplicationClient
from requests_oauthlib import OAuth2Session


class swisscom_api:
    def __init__(self, client_id: str, client_secret: str, token_url: str):
        # See https://requests-oauthlib.readthedocs.io/en
        # /latest/oauth2_workflow.html#backend-application-flow.
        self.client = BackendApplicationClient(client_id=client_id)
        self.oauth = OAuth2Session(client=self.client)
        # Fetch an access token.
        self.oauth.fetch_token(
            token_url=token_url, client_id=client_id, client_secret=client_secret
        )

    def get_npvm_pair_od(
        self, list_npvm_pair: List[str], year: int, month: int, mode: str = "standard"
    ) -> dict:
        year_month = dt(year, month, 1, 0, 0).strftime("%Y-%m")
        request_url = (
            "https://api.swisscom.com/layer/origin-destination/"
            + mode
            + "/odmatrices/monthly/"
            + year_month
            + "/postal-code-areas/pairs?"
        )
        for i, npvm_pair in enumerate(list_npvm_pair):
            if i != 0:
                request_url += "&"
            request_url += "pairs=" + str(npvm_pair[0]) + "," + str(npvm_pair[1])
        # Use the access token to query an endpoint.
        resp = self.oauth.get(request_url, headers={"scs-version": "1"})
        return resp.json()


class SwisscomAPIAccess(tool.Tool):
    def npvm_pair_od_to_df(self, npvm_pair_od: dict) -> pd.DataFrame:
        list_dict = []
        if const.CONST_NAME_FIELD_PAIRS not in npvm_pair_od.keys():
            self.print_warning(npvm_pair_od)
        else:
            for i, pair in enumerate(npvm_pair_od[const.CONST_NAME_FIELD_PAIRS]):
                pair_dict = {}
                pair_dict[const.CONST_NAME_FIELD_ORIGIN] = pair[
                    const.CONST_NAME_FIELD_ORIGIN
                ]
                pair_dict[const.CONST_NAME_FIELD_DESTINATION] = pair[
                    const.CONST_NAME_FIELD_DESTINATION
                ]
                for day in [
                    const.CONST_NAME_FIELD_WEEKDAYS,
                    const.CONST_NAME_FIELD_WEEKEND,
                ]:
                    for mode in [
                        const.CONST_NAME_FIELD_TOTAL,
                        const.CONST_NAME_FIELD_TRAIN,
                        const.CONST_NAME_FIELD_OTHERS,
                    ]:
                        if day in pair.keys():
                            if mode in pair[day].keys():
                                pair_dict[day + "_" + mode] = pair[day][mode]
                            else:
                                pair_dict[day + "_" + mode] = 0
                list_dict.append(pair_dict)
        return pd.DataFrame.from_dict(list_dict)

    def read_csv(self, csv_path: str) -> pd.DataFrame:
        df = pd.read_csv(csv_path, sep=",", decimal=".")
        if len(df.columns) == 1:
            df = pd.read_csv(csv_path, sep=";", decimal=",")
        return df

    def get_list_pair_from_zone(
        self,
        list_zone_interest: list,
        list_zone_secondary: list,
        list_zone_secondary_transit: list,
        df_zone: pd.DataFrame,
    ) -> list:
        list_pair = []
        for zone_interest in list_zone_interest:
            list_npvm_zone_interest = df_zone[
                df_zone[self.json_file[const.KEY_NAME_FIELD_ZONE_ID]] == zone_interest
            ][self.json_file[const.KEY_NAME_FIELD_NPVM_ID]]
            for destination in list_zone_interest:
                list_npvm_destination = df_zone[
                    df_zone[self.json_file[const.KEY_NAME_FIELD_ZONE_ID]] == destination
                ][self.json_file[const.KEY_NAME_FIELD_NPVM_ID]]
                for npvm_zone_interest in list_npvm_zone_interest:
                    for npvm_destination in list_npvm_destination:
                        list_pair.append([npvm_zone_interest, npvm_destination])
            for zone_secondary in list_zone_secondary:
                list_npvm_zone_secondary = df_zone[
                    df_zone[self.json_file[const.KEY_NAME_FIELD_ZONE_ID]]
                    == zone_secondary
                ][self.json_file[const.KEY_NAME_FIELD_NPVM_ID]]
                for npvm_zone_interest in list_npvm_zone_interest:
                    for npvm_zone_secondary in list_npvm_zone_secondary:
                        list_pair.append([npvm_zone_interest, npvm_zone_secondary])
                        list_pair.append([npvm_zone_secondary, npvm_zone_interest])
        for zone_secondary_transit in list_zone_secondary_transit:
            list_npvm_zone_secondary_transit_origin = df_zone[
                df_zone[self.json_file[const.KEY_NAME_FIELD_ZONE_ID]]
                == zone_secondary_transit[0]
            ][self.json_file[const.KEY_NAME_FIELD_NPVM_ID]]
            list_npvm_zone_secondary_transit_destination = df_zone[
                df_zone[self.json_file[const.KEY_NAME_FIELD_ZONE_ID]]
                == zone_secondary_transit[1]
            ][self.json_file[const.KEY_NAME_FIELD_NPVM_ID]]
            for (
                npvm_zone_secondary_transit_origin
            ) in list_npvm_zone_secondary_transit_origin:
                for (
                    npvm_zone_secondary_transit_destination
                ) in list_npvm_zone_secondary_transit_destination:
                    list_pair.append(
                        [
                            npvm_zone_secondary_transit_origin,
                            npvm_zone_secondary_transit_destination,
                        ]
                    )
                    list_pair.append(
                        [
                            npvm_zone_secondary_transit_destination,
                            npvm_zone_secondary_transit_origin,
                        ]
                    )
        return list_pair

    def get_aggregate_npvm_to_zone(
        self, df_od_npvm: pd.DataFrame, df_zone: pd.DataFrame
    ) -> pd.DataFrame:
        list_index = [
            self.json_file[const.KEY_NAME_FIELD_ZONE_ID] + "_origin",
            self.json_file[const.KEY_NAME_FIELD_ZONE_ID] + "_destination",
        ]
        list_col = [
            day + "_" + mode
            for day in [const.CONST_NAME_FIELD_WEEKDAYS, const.CONST_NAME_FIELD_WEEKEND]
            for mode in [
                const.CONST_NAME_FIELD_TOTAL,
                const.CONST_NAME_FIELD_TRAIN,
                const.CONST_NAME_FIELD_OTHERS,
            ]
        ] + list_index
        df_od_zone = (
            df_od_npvm.fillna(0)
            .merge(
                df_zone,
                left_on=const.CONST_NAME_FIELD_ORIGIN,
                right_on=self.json_file[const.KEY_NAME_FIELD_NPVM_ID],
            )
            .rename(
                columns={
                    self.json_file[const.KEY_NAME_FIELD_ZONE_ID]: self.json_file[
                        const.KEY_NAME_FIELD_ZONE_ID
                    ]
                    + "_origin"
                }
            )
        )
        df_od_zone = df_od_zone.merge(
            df_zone,
            left_on=const.CONST_NAME_FIELD_DESTINATION,
            right_on=self.json_file[const.KEY_NAME_FIELD_NPVM_ID],
        ).rename(
            columns={
                self.json_file[const.KEY_NAME_FIELD_ZONE_ID]: self.json_file[
                    const.KEY_NAME_FIELD_ZONE_ID
                ]
                + "_destination"
            }
        )
        df_od_zone = (
            df_od_zone[list_col]
            .groupby(
                [
                    self.json_file[const.KEY_NAME_FIELD_ZONE_ID] + "_origin",
                    self.json_file[const.KEY_NAME_FIELD_ZONE_ID] + "_destination",
                ]
            )
            .sum()
        )
        df_od_zone = df_od_zone.reset_index(level=list_index, inplace=False)
        for col in list_col:
            if col not in df_od_zone.columns and col not in [
                self.json_file[const.KEY_NAME_FIELD_ZONE_ID] + "_origin",
                self.json_file[const.KEY_NAME_FIELD_ZONE_ID] + "_destination",
            ]:
                df_od_zone[col] = len(df_od_zone.index) * [
                    0
                ]  # Si une des colonnes manque car pas d'observation
        return df_od_zone

    def save_result(self, list_df: List[pd.DataFrame], save_index: int) -> str:
        df = pd.concat(list_df)
        output_path = os.path.join(
            self.json_file[const.KEY_SAVE_OUTPUT_DIR],
            self.json_file[const.KEY_SAVE_NAME] + "_" + str(save_index) + ".csv",
        )
        df.to_csv(output_path, index=False)
        self.print_message("Data written in " + output_path)
        return output_path

    def concatenate_result(self, list_output_path: List[str]) -> pd.DataFrame:
        list_df = []
        for output_path in list_output_path:
            # Modified by SCH 15.05.21 to track the exception if the csv file is empty
            try:
                list_df.append(pd.read_csv(output_path))
            except Exception:
                self.print_error("Error while concatenating csv file: " + output_path)

        return pd.concat(list_df)

    def execute(self) -> None:
        df_zone = self.read_csv(self.json_file[const.KEY_ZONE_PATH])
        list_pair = self.get_list_pair_from_zone(
            self.json_file[const.KEY_LIST_ZONE_INTEREST],
            self.json_file[const.KEY_LIST_ZONE_SECONDARY],
            self.json_file[const.KEY_LIST_ZONE_SECONDARY_TRANSIT],
            df_zone,
        )
        n_pair = len(list_pair)
        self.print_message("Number of OD pairs to process: " + str(n_pair))
        n_batch = int(np.ceil(n_pair / self.json_file[const.KEY_REQUEST_BATCH_SIZE]))
        self.print_message("Number of batch: " + str(n_batch))
        list_df_result: List[pd.DataFrame] = []
        list_output_path = []
        # Connexion à l'API
        try:
            swisscom_api_instance = swisscom_api(
                self.json_file[const.KEY_CLIENT_ID],
                self.json_file[const.KEY_CLIENT_SECRET],
                self.json_file[const.KEY_TOKEN_URL],
            )
        except Exception:
            self.print_error("Failed to connect to Swisscom API")
            self.print_message("End of program")
            sys.exit(1)
        self.print_message(
            "Access token: " + str(swisscom_api_instance.oauth.access_token)
        )
        if self.json_file[const.KEY_USE_DEMO_MODE]:
            request_mode = (
                "demo"  # mode démo (gratuit, données d'avril 2019 uniquement)
            )
        else:
            request_mode = "standard"  # mode standard (payant)

        for batch in range(n_batch):
            if (
                batch != 0 and batch % self.json_file[const.KEY_SAVE_BATCH_SIZE] == 0
            ):  # On sauvegarde les résultats tous les x batchs
                output_path = self.save_result(
                    list_df_result, batch // self.json_file[const.KEY_SAVE_BATCH_SIZE]
                )
                list_output_path.append(output_path)
                list_df_result = []
            pair_batch_from = batch * self.json_file[const.KEY_REQUEST_BATCH_SIZE]
            pair_batch_to = np.min(
                [(batch + 1) * self.json_file[const.KEY_REQUEST_BATCH_SIZE], n_pair]
            )
            list_pair_batch = list_pair[pair_batch_from:pair_batch_to]
            sys.stdout.write(("\rBatch {:d} out of {:d} ").format(batch + 1, n_batch))
            sys.stdout.flush()
            # Récupération du flux OD par requête à l'API de Swisscom
            try:
                request_result = swisscom_api_instance.get_npvm_pair_od(
                    list_pair_batch,
                    self.json_file[const.KEY_YEAR],
                    self.json_file[const.KEY_MONTH],
                    mode=request_mode,
                )
                list_df_result.append(self.npvm_pair_od_to_df(request_result))
            except Exception:
                self.print_error("Invalid request or failed to send request")
        if len(list_df_result) > 0:
            # On sauvegarde les derniers résultats
            # (qui ne sont dans le dernier groupe de batch)
            output_path = self.save_result(list_df_result, len(list_output_path) + 1)
            list_output_path.append(output_path)

        self.print_message("Concatenating all generated csv tables")
        df_od_npvm = self.concatenate_result(
            list_output_path
        )  # Concaténation de toutes les tables csv générées
        self.print_message("Aggregating NPVM data result in zones")
        df_od_zone = self.get_aggregate_npvm_to_zone(
            df_od_npvm, df_zone
        )  # Aggrégation des NPVM en zones
        self.print_message(
            "Saving aggregating data in csv file: "
            + self.json_file[const.KEY_OUTPUT_PATH]
        )
        df_od_zone.to_csv(self.json_file[const.KEY_OUTPUT_PATH], index=False)
        self.print_message("End of program")


if __name__ == "__main__":
    tool.main(SwisscomAPIAccess)
