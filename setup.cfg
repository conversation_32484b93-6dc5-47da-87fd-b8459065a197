[mypy]
python_version = 3.10
disallow_untyped_defs = True
ignore_missing_imports = True
warn_unused_configs = True
namespace_packages = True
show_error_codes = True
files = .

[flake8]
max-line-length = 88
ignore=E121,E123,E126,E226,E24,E704,W503,W504,E701

[isort]
skip = alembic/env.py
skip_glob = alembic/versions/*,venv/**,.eggs/**
line_length=88
multi_line_output=3
include_trailing_comma=True
use_parentheses=True
