{"global_parameters": {"input_csv_path": "Input\\points_fitte.csv", "output_csv_path": "Output\\results_fitte.csv", "output_temp_dir": "Output\\temp", "checkpoint_size": 100, "sampling_display_progress": "10", "x": "x", "y": "y", "location_id": "Name", "max_origin": 5, "max_destination": 20, "list_departure_time": [], "overlays": "", "geom": false, "details": true, "route_handle": false, "usehistoricaltraveltime": false}, "giscore_v0.3.0": {"use": false, "parameters": {"url": "https://gis-core-restapi-210818.azurewebsites.net//api//route", "apiKey": "PJVCX5S-XQD44EQ-NZS3RYD-G6V58DB", "header_origin": "localhost", "mode": "fastest;car;traffic:enabled", "return": "summary,typicalDuration,mlDuration", "overlays": "", "geom": false, "details": true, "route_handle": false, "usehistoricaltraveltime": false}}, "giscore_v1.0.0": {"use": false, "parameters": {"url": "https://app.e-rebus.com/giscore/", "routePath": "api/route", "authPath": "api/auth/login", "login": "erebus_viewer", "password": "sUtK=è5q6?°3Y§e8", "retry": 10, "mode": "fastest;car;traffic:enabled", "traverseGates": false, "version": "v8", "overlays": "", "geom": false, "details": true, "route_handle": false, "usehistoricaltraveltime": false}}, "here_v8": {"use": false, "parameters": {"url": "https://router.hereapi.com/v8/routes", "map_attributes_url": "https://smap.hereapi.com/v8/maps/attributes", "span_attributes": "segmentRef,segmentId,functionalClass,dynamicSpeedInfo,typicalDuration,baseDuration,duration,length", "apiKey": "kX3yLd1fksu_0JK1UWdva09_c2KmkRGlIzO9KHmkK80", "mode": "car", "trafic": "enabled", "list_avoid_area": [], "version": "v8", "return": "summary,typicalDuration,mlDuration,polyline", "spans": "dynamicSpeedInfo"}}, "google": {"use": true, "parameters": {"url": "https://maps.googleapis.com/maps/api", "apiKey": "AIzaSyBO_z8sGlAiXkiIQyURZ_K1wHPdQYT6yNM", "mode": "transit"}}, "tomtom": {"use": false, "parameters": {"url": "https://api.tomtom.com/routing/1/calculateRoute/", "apiKey": "Df6uIAa00hE7eiaIhnLxRUZA8UQ4Cn9G", "mode": "car", "trafic": "true", "computeTravelTimeFor": "all"}}}