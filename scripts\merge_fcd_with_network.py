import argparse
import csv
import logging
import os
import sys
from collections import defaultdict
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

import geopandas
import pandas

from workers.preprocess_streets import preprocess_street


class EpochType(Enum):
    EPOCH_15MIN = "EPOCH-15MIN"
    EPOCH_5MIN = "EPOCH-5MIN"
    EPOCH_60MIN = "EPOCH-60MIN"
    UNKNOWN = "UNKNOWN"

    @staticmethod
    def from_string(epoch: str) -> "EpochType":
        return {
            "EPOCH-15MIN": EpochType.EPOCH_15MIN,
            "EPOCH-5MIN": EpochType.EPOCH_5MIN,
            "EPOCH-60MIN": EpochType.EPOCH_60MIN,
        }.get(epoch, EpochType.UNKNOWN)

    def get_code(self) -> str:
        return self.value

    def get_epoch_duration(self) -> int:
        return {
            EpochType.EPOCH_15MIN: 15,
            EpochType.EPOCH_5MIN: 5,
            EpochType.EPOCH_60MIN: 60,
        }.get(self, -1)


def translate_epoch_time(epoch_type: EpochType, epoch: int) -> str:
    if epoch_type == EpochType.EPOCH_15MIN:
        hour = epoch // 4
        minute = (epoch % 4) * 15
        return f"{hour:02d}H{minute:02d}"
    elif epoch_type == EpochType.EPOCH_5MIN:
        hour = epoch // 12
        minute = (epoch % 12) * 5
        return f"{hour:02d}H{minute:02d}"
    elif epoch_type == EpochType.EPOCH_60MIN:
        return f"{epoch}H"
    else:
        raise ValueError("Unsupported epoch column type")


def parse_args(args: Optional[List[str]]) -> argparse.Namespace:
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "shapefile",
        metavar="file.shp",
        help="Path to shapefile containing network geometries",
    )
    parser.add_argument(
        "speed_data",
        metavar="data.csv",
        help="Path to CSV file containing speed data to map to geometries",
    )
    parser.add_argument(
        "--preprocess-streets",
        "-p",
        action="store_true",
        help="Preprocess street geometries",
    )

    parser.add_argument(
        "--output-dir",
        "-o",
        metavar="/path/to/dir",
        help="Path to directory where to output files",
    )
    logging.basicConfig(level=logging.INFO)
    return parser.parse_args(args)


def read_shapefile_dataframe(
    file_path: str, preprocess: bool = False
) -> geopandas.GeoDataFrame:
    logging.info("Reading shapefile")
    df = geopandas.read_file(file_path)
    logging.info(f"Read {len(df)} geometries")
    logging.info(df.keys())
    df = df[
        [
            "geometry",
            "LINK_ID",
            "ST_NAME",
            "FUNC_CLASS",
            "DIR_TRAVEL",
            "FR_SPD_LIM",
            "TO_SPD_LIM",
        ]
    ]
    logging.info(df.keys())
    if preprocess:
        df = preprocess_street(df)
    return df


def get_epoch_column(data: Dict[str, Any]) -> EpochType:
    epoch_column = next(
        (EpochType.from_string(col) for col in data.keys() if col.startswith("EPOCH-")),
        EpochType.UNKNOWN,
    )
    return epoch_column


def find_all_epochs(file_path: str) -> Tuple[EpochType, List[int]]:
    all_epochs = []
    epoch_column = EpochType.UNKNOWN

    with open(file_path) as f:
        reader = csv.DictReader(f)
        for data in reader:
            if epoch_column == EpochType.UNKNOWN:
                epoch_column = get_epoch_column(data)
                if epoch_column is EpochType.UNKNOWN:
                    raise ValueError("No epoch column found in the data")
            epoch = int(data[epoch_column.get_code()])
            if epoch not in all_epochs:
                all_epochs.append(epoch)

    logging.info(f"Detected epoch column {epoch_column} with epochs {all_epochs}")
    return epoch_column, all_epochs


def is_within_peak_hours(
    epoch: int, peak_start: int, peak_end: int, epoch_duration: int
) -> bool:
    peak_start_epoch = peak_start * 60 // epoch_duration
    peak_end_epoch = peak_end * 60 // epoch_duration
    return peak_start_epoch <= epoch < peak_end_epoch


def compute_peak_means(
    df: pandas.DataFrame,
    epoch_column: EpochType,
    all_epochs: List[int],
    peak_start: int,
    peak_end: int,
    epoch_duration: int,
) -> pandas.Series:
    peak_columns = [
        f"MEAN{translate_epoch_time(epoch_column, epoch)}"
        for epoch in all_epochs
        if is_within_peak_hours(epoch, peak_start, peak_end, epoch_duration)
    ]
    return df[peak_columns].mean(axis=1)


def read_speed_data_dataframe(
    file_path: str,
    morning_peak_start: int = 7,
    morning_peak_end: int = 8,
    evening_peak_start: int = 17,
    evening_peak_end: int = 18,
    preprocess: bool = False,
) -> pandas.DataFrame:
    logging.info("Reading speed data")
    epoch_column, all_epochs = find_all_epochs(file_path)
    speed_data: Dict[str, List[Any]] = defaultdict(list)
    nb_read = 0
    with open(file_path) as f:
        reader = csv.DictReader(f)
        for data in reader:
            direction = data["LINK-DIR"][-1]
            time = int(data[epoch_column.get_code()])
            duration = data["MEAN"]
            count = data["COUNT"]
            speed_data["FCD_DIRECTION"].append(direction)
            speed_data["FREEFLOW"].append(float(data["FREEFLOW"]))
            speed_data["LINK_DIR"].append(data["LINK-DIR"])
            for epoch in all_epochs:
                translated_time = translate_epoch_time(epoch_column, epoch)
                count_col = f"COUNT{translated_time}"
                mean_col = f"MEAN{translated_time}"
                if epoch == time:
                    speed_data[count_col].append(int(count))
                    speed_data[mean_col].append(float(duration))
                else:
                    speed_data[count_col].append(None)
                    speed_data[mean_col].append(None)
            nb_read += 1
    logging.info(f"Read {nb_read} speeds")
    speed_data_df = pandas.DataFrame(speed_data)
    if not preprocess:
        id_col = "LINK_ID"
        speed_data_df["LINK_ID"] = speed_data_df["LINK_DIR"].apply(
            lambda x: int(x[:-1])
        )
    else:
        id_col = "LINK_DIR"
    logging.debug(speed_data_df)
    aggregation_parameters = {}
    for epoch in all_epochs:
        aggregation_parameters["FREEFLOW"] = ("FREEFLOW", "first")
        translated_time = translate_epoch_time(epoch_column, epoch)
        count_col = f"COUNT{translated_time}"
        mean_col = f"MEAN{translated_time}"
        aggregation_parameters[count_col] = (count_col, "sum")
        aggregation_parameters[mean_col] = (mean_col, "mean")
    grouped = speed_data_df.groupby([id_col, "FCD_DIRECTION"], as_index=False)
    aggregation = grouped.agg(**aggregation_parameters)

    epoch_duration = epoch_column.get_epoch_duration()
    aggregation["MEAN_MORNING_PEAK"] = compute_peak_means(
        aggregation,
        epoch_column,
        all_epochs,
        morning_peak_start,
        morning_peak_end,
        epoch_duration,
    )
    aggregation["MEAN_EVENING_PEAK"] = compute_peak_means(
        aggregation,
        epoch_column,
        all_epochs,
        evening_peak_start,
        evening_peak_end,
        epoch_duration,
    )
    aggregation["MEAN_OTHER_HOURS"] = aggregation[
        [
            f"MEAN{translate_epoch_time(epoch_column, epoch)}"
            for epoch in all_epochs
            if not (
                is_within_peak_hours(
                    epoch, morning_peak_start, morning_peak_end, epoch_duration
                )
                or is_within_peak_hours(
                    epoch, evening_peak_start, evening_peak_end, epoch_duration
                )
            )
        ]
    ].mean(axis=1)

    logging.debug(aggregation)
    return aggregation


def main(args: Optional[List[str]]) -> None:
    parsed_args = parse_args(args)
    network = read_shapefile_dataframe(
        parsed_args.shapefile, parsed_args.preprocess_streets
    )
    speed_data_df = read_speed_data_dataframe(
        parsed_args.speed_data, preprocess=parsed_args.preprocess_streets
    )
    if not parsed_args.preprocess_streets:
        id_col = "LINK_ID"
    else:
        id_col = "LINK_DIR"
    joined_shapes = network.join(speed_data_df.set_index(id_col), on=id_col)
    print(joined_shapes)
    root_folder = "joined_network_with_fcd"
    folders = [
        f"{os.path.splitext(os.path.basename(parsed_args.shapefile))[0]}",
        f"{os.path.splitext(os.path.basename(parsed_args.speed_data))[0]}",
    ]
    folder = os.path.join(parsed_args.output_dir, root_folder, *folders)
    os.makedirs(folder)
    joined_shapes.to_file(
        os.path.join(folder, "speed_data.shp"), driver="ESRI Shapefile"
    )
    nb_nan = joined_shapes["FCD_DIRECTION"].isna().sum()
    print(f"Joined data has {nb_nan} segments missing speed data")


def execute_script(name: str, args: Optional[List[str]] = None) -> None:
    if name == "__main__":
        try:
            main(args)
        except Exception as e:
            logging.error(e)
            sys.exit(2)


execute_script(__name__)
