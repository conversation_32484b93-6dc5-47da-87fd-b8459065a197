echo "coucou"
set PYTHON_PATHNAME="C:\Users\<USER>\AppData\Local\ESRI\conda\envs\arcgispro-py3-clone\python.exe"

set UPDATEPARKINGSPROVISOIRES_SCRIPT_PATH="C:\Users\<USER>\CITEC\Projet R&D - Traitement de données mobilités - Technique\RyderCupUpdates\Development"
set UPDATEPARKINGSPROVISOIRES_SCRIPT_SCRIPT_NAME="rydercup.py"

cd /d %UPDATEPARKINGSPROVISOIRES_SCRIPT_PATH%
echo %UPDATEPARKINGSPROVISOIRES_SCRIPT_PATH%
set CONFIG_PATHNAME="C:\Users\<USER>\CITEC\Projet R&D - Traitement de données mobilités - Technique\RyderCupUpdates\Development\Json\config_euro_feminin.json"
%PYTHON_PATHNAME% %UPDATEPARKINGSPROVISOIRES_SCRIPT_SCRIPT_NAME% %CONFIG_PATHNAME%
start "UpdateParkingPythonScript" %PYTHON_PATHNAME% %UPDATEPARKINGSPROVISOIRES_SCRIPT_SCRIPT_NAME% %CONFIG_PATHNAME% 1> out.txt 2> erreur.txt
timeout /t 240 /nobreak
taskkill /FI "WINDOWTITLE eq UpdateParkingPythonScript" /F /T