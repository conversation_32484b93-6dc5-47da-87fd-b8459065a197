import asyncio
import os
import time
from typing import Any, Dict, List

import aiohttp
import async_timeout
import gpxpy
import gpxpy.gpx
import pandas as pd
from tqdm import tqdm

tqdm.pandas()


def normalize_leg_data(leg_dict: Dict[str, Any], record_path: str) -> pd.DataFrame:
    if record_path in leg_dict.keys():
        normalized_leg_data = pd.json_normalize(
            leg_dict,
            record_path,
            ["length", "travelTime", "refReplacements"],
            meta_prefix="leg_",
        )
        normalized_leg_data["leg_length"] = normalized_leg_data["leg_length"].astype(
            "int64"
        )
        normalized_leg_data["leg_travelTime"] = normalized_leg_data[
            "leg_travelTime"
        ].astype("int64")
    else:
        raise KeyError(f"record_path {record_path} not found in leg_dict")
    return normalized_leg_data


def process_batch_legs(
    leg_dict: Dict[str, Any], payload: Dict[str, Any], record_paths: List[str]
) -> pd.DataFrame:
    """Create a DataFrame from the route leg and payload."""
    return pd.concat(
        [
            normalize_leg_data(leg, path)
            for path in record_paths
            for leg in leg_dict["leg"]
        ],
        axis=1,
    ).assign(ANONID=payload["ANONID"], DATE=payload["DATE"])


def convert_df_to_gpx_string(
    df: pd.DataFrame,
    latitude_field: str = "LATITUDE",
    longitude_field: str = "LONGITUDE",
) -> str:
    """
    Convert a dataframe representing a MultiLine into a gpx string.
    The dataframe is expected to contain a latitude and longitude column
    describing each point, in order, of the segmented line.
    Parameters :
    - df : the dataframe to convert
    - latitude_field : the name of the latitude field
    - longitude_field : the name of the longitude field
    Output :
    The output is the GPX representation of the multiline
    usable by the HERE API for route matching.
    """
    gpx = gpxpy.gpx.GPX()
    gpx_track = gpxpy.gpx.GPXTrack()
    gpx.tracks.append(gpx_track)
    gpx_segment = gpxpy.gpx.GPXTrackSegment()
    gpx_track.segments.append(gpx_segment)
    gpx_segment.points = [
        gpxpy.gpx.GPXTrackPoint(row[latitude_field], row[longitude_field])
        for _, row in df.iterrows()
    ]
    return gpx.to_xml()


def serialize_df_to_gpx_strings(
    df: pd.DataFrame,
    track_id_field: str = "ANONID",
    latitude_field: str = "LATITUDE",
    longitude_field: str = "LONGITUDE",
) -> List[Dict[str, Any]]:
    """
    Generates a dictionary of gpx strings from a dataframe.
    The dataframe is expected to contain a track id field, a latitude
    field and a longitude field. The function will group the dataframe
    by the track id field and generate a gpx string for each one.
    Parameters :
    - df : the dataframe to convert
    - track_id_field : the name of the track id field
    - latitude_field : the name of the latitude field
    - longitude_field : the name of the longitude field
    Output :
    The output is a list of dictionaries. Each dictionary contains a GPX string,
    track id, and its start date.
    """
    gpx_date_df = (
        df.groupby([track_id_field])
        .agg({"DATE": "min", latitude_field: list, longitude_field: list})
        .reset_index()
    )
    gpx_date_df["gpx"] = gpx_date_df.apply(
        lambda x: convert_df_to_gpx_string(
            pd.DataFrame(
                {latitude_field: x[latitude_field], longitude_field: x[longitude_field]}
            ),
            latitude_field,
            longitude_field,
        ),
        axis=1,
    )
    return (
        gpx_date_df[["gpx", track_id_field, "DATE"]]
        .apply(lambda x: x.to_dict(), axis=1)
        .tolist()
    )


async def fetch(
    session: aiohttp.ClientSession,
    url: str,
    payload: str,
    headers: Dict[str, str] = {"Content-Type": "application/binary"},
) -> Dict[str, Any]:
    """Fetch a route from the HERE API asynchronously.

    Parameters:
    - session: An aiohttp session.
    - url: The url of the HERE API.
    - payload: The formatted payload of the request.
    - headers: The headers of the request.
    Output:
    Returns a JSON dictionary containing the response from the HERE API.
    """
    async with async_timeout.timeout(100):
        async with session.post(url, data=payload, headers=headers) as response:
            return await response.json()


async def fetch_all_routes(
    gpx_strings: List[Dict[str, Any]],
    url: str,
    record_paths: List[str] = ["maneuver", "link"],
    batch_size: int = 100,
    routes_path: str = "legMatched.csv",
) -> pd.DataFrame:
    """
    Fetches all routes from the HERE Route matching API.
    For more information on the HERE Route matching API, see
    https://developer.here.com/documentation/route-matching/api-reference.html

    Parameters:
    - gpx_strings: A list of dictionaries with GPX strings, track id, and start date.
    - url: The url of the HERE API.
    - record_paths: List of paths to records in the JSON response from the HERE API.
    - batch_size: The number of routes to fetch in each batch, by default 100
    - routes_path: The path to save the routes computed from GPS tracks.

    Output:
    Returns a DataFrame containing the matched routes fetched from the HERE API.
    """
    routes = pd.DataFrame()
    total = len(gpx_strings)
    async with aiohttp.ClientSession() as session:
        for i in range(0, total, batch_size):
            start = time.time()
            batch_gpx = gpx_strings[i : i + batch_size]  # noqa: E203
            batch_tasks = [fetch(session, url, payload["gpx"]) for payload in batch_gpx]
            batch_results = [
                item["response"]["route"][0]
                for item in await asyncio.gather(*batch_tasks)
            ]
            batch_leg_df = pd.DataFrame()
            missing_maneuvers = pd.DataFrame()
            for leg_dict, payload in zip(batch_results, batch_gpx):
                try:
                    leg_df = process_batch_legs(leg_dict, payload, record_paths)
                    batch_leg_df = pd.concat([batch_leg_df, leg_df])
                    batch_leg_df["LINK_ID"] = batch_leg_df["linkId"].astype("int64")
                except KeyError as e:
                    print(str(e))
                    missing_maneuvers = pd.concat(
                        [
                            missing_maneuvers,
                            pd.DataFrame(
                                {
                                    "ANONID": [payload["ANONID"]],
                                    "DATE": [payload["DATE"]],
                                }
                            ),
                        ]
                    )
                    missing_maneuvers_path = f"{routes_path[:-4]}_missing_maneuvers.csv"
                    missing_maneuvers.to_csv(
                        missing_maneuvers_path,
                        index=False,
                        mode="a",
                        header=not os.path.exists(missing_maneuvers_path),
                    )
                    continue

            batch_leg_df.to_csv(
                routes_path,
                index=False,
                mode="a",
                header=not os.path.exists(routes_path),
            )
            routes = pd.concat([routes, batch_leg_df])
            end = time.time()
            print(
                f"Completed batch {i // batch_size + 1} "
                f"of {total // batch_size + 1} "
                f"in {round((end - start), 2)} seconds"
            )

            await asyncio.sleep(0.01)
    return routes.reset_index(drop=True)


def filter_already_matched(
    gdf_matched_routes: pd.DataFrame, gdf_probes: pd.DataFrame
) -> pd.DataFrame:
    """
    Check if the probes have already been matched to avoid re-matching.
    Parameters:
    - gdf_matched_routes: A GeoDataFrame containing the matched routes.
    - gdf_probes: A GeoDataFrame containing the probes.
    Output:
    Returns a GeoDataFrame containing the probes that have not been matched.
    """
    unique_matched_anonid = gdf_matched_routes["ANONID"].drop_duplicates()
    unique_probes_anonid = gdf_probes["ANONID"].drop_duplicates()
    already_matched_count = unique_probes_anonid.isin(unique_matched_anonid).sum()
    print(
        f"Probes already matched: {already_matched_count} "
        f"out of {len(unique_probes_anonid)}"
    )
    already_matched_mask = gdf_probes["ANONID"].isin(gdf_matched_routes["ANONID"])
    return gdf_probes[~already_matched_mask].reset_index(drop=True)
