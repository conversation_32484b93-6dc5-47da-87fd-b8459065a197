from datetime import datetime
from pathlib import Path
from typing import Any
from unittest.mock import Mock, call, patch

import pytest
from freezegun import freeze_time

from scripts.backup_agol import (
    AnalyzeItemDependencyUseCase,
    ArcGISIdentity,
    ArcGISRepository,
    BackupItemsUseCase,
    GetFolderStatisticsUseCase,
    ItemDependency,
    execute_script,
    main,
    sanitize_filename,
)


@pytest.fixture
def arcgis_identity() -> ArcGISIdentity:
    return ArcGISIdentity("https://arcgis.com", "user", "--not-safe")


@pytest.fixture
def mock_gis() -> Any:
    with patch("scripts.backup_agol.GIS") as mock_gis:
        yield mock_gis


@pytest.fixture
def mock_repository(mock_gis: Any, arcgis_identity: ArcGISIdentity) -> ArcGISRepository:
    mock_gis.return_value = Mock()
    return ArcGISRepository(arcgis_identity)


class TestArcGISRepository:
    def test_get_user(self, mock_gis: Any, mock_repository: ArcGISRepository) -> None:
        mock_user = Mock()
        mock_gis.return_value.users.get.return_value = mock_user

        user = mock_repository.get_user("test_user")

        mock_gis.assert_called_once_with("https://arcgis.com", "user", "--not-safe")
        mock_gis.return_value.users.get.assert_called_once_with("test_user")
        assert user == mock_user

    def test_search_items(
        self, mock_gis: Any, mock_repository: ArcGISRepository
    ) -> None:
        mock_items = [Mock(id="webmap1"), Mock(id="layer1")]
        mock_gis.return_value.content.search.return_value = mock_items

        items = mock_repository.search_items("type: Web Map", 10)

        mock_gis.return_value.content.search.assert_called_once_with(
            query="type: Web Map", max_items=10
        )
        assert items == mock_items

    def test_get_item(self, mock_gis: Any, mock_repository: ArcGISRepository) -> None:
        mock_item = Mock()
        mock_gis.return_value.content.get.return_value = mock_item

        item = mock_repository.get_item("webmap1")

        mock_gis.return_value.content.get.assert_called_once_with("webmap1")
        assert item == mock_item

    def test_get_all_item_ids(
        self, mock_gis: Any, mock_repository: ArcGISRepository
    ) -> None:
        mock_user = Mock()
        mock_user.folders = []
        mock_user.items.return_value = [Mock(id="webmap1"), Mock(id="layer1")]
        mock_gis.return_value.users.get.return_value = mock_user

        item_ids = mock_repository.get_all_item_ids()

        assert item_ids == ["webmap1", "layer1"]

    def test_get_item_ids_by_type(
        self, mock_gis: Any, mock_repository: ArcGISRepository
    ) -> None:
        mock_user = Mock()
        mock_user.folders = []
        mock_user.items.return_value = [
            Mock(id="layer1", type="Feature Service"),
            Mock(id="webmap1", type="Web Map"),
        ]
        mock_gis.return_value.users.get.return_value = mock_user

        item_ids = mock_repository.get_item_ids_by_type("Web Map")

        assert item_ids == ["webmap1"]

    def test_get_item_ids_by_folder(
        self, mock_gis: Any, mock_repository: ArcGISRepository
    ) -> None:
        mock_user = Mock()
        mock_user.folders = [{"title": "My Folder"}]
        mock_user.items.return_value = [Mock(id="webmap1"), Mock(id="layer1")]
        mock_gis.return_value.users.get.return_value = mock_user

        item_ids = mock_repository.get_item_ids_by_folder("My Folder")

        assert item_ids == ["webmap1", "layer1"]

    def test_get_item_ids_raises_error_for_both_item_type_and_folder(
        self, mock_gis: Any, mock_repository: ArcGISRepository
    ) -> None:
        with pytest.raises(
            ValueError,
            match="Both item_type and folder_name cannot be specified together.",
        ):
            mock_repository._get_item_ids(item_type="Web Map", folder_name="My Folder")

    def test_get_item_ids_with_nonexistent_folder_defaults_to_none_folder(
        self, mock_gis: Any, mock_repository: ArcGISRepository
    ) -> None:
        mock_user = Mock()
        mock_user.folders = [{"title": "My Folder"}]
        mock_user.items.return_value = [Mock(id="webmap1"), Mock(id="layer1")]
        mock_gis.return_value.users.get.return_value = mock_user

        item_ids = mock_repository.get_item_ids_by_folder("Nonexistent Folder")

        assert item_ids == ["webmap1", "layer1"]

    def test_get_item_ids_with_empty_folder(
        self, mock_gis: Any, mock_repository: ArcGISRepository
    ) -> None:
        mock_user = Mock()
        mock_user.folders = [{"title": "Empty Folder"}]
        mock_user.items.return_value = []
        mock_gis.return_value.users.get.return_value = mock_user

        item_ids = mock_repository.get_item_ids_by_folder("Empty Folder")

        assert item_ids == []


class TestAnalyzeItemDependencyUseCase:
    @patch("scripts.backup_agol.ArcGISRepository")
    def test_execute_with_error_getting_item(self, mock_repository: Mock) -> None:
        mock_repository.logger = Mock()
        mock_repository.get_item.side_effect = Exception("Failed to get item")
        use_case = AnalyzeItemDependencyUseCase(mock_repository)

        with (
            patch.object(use_case, "_cache_web_content"),
            patch.object(use_case, "_export_to_csv"),
        ):
            use_case.execute(["invalid_id"], "output.csv")

        mock_repository.logger.error.assert_called_with(
            "Error processing item invalid_id: Failed to get item"
        )

    @patch("scripts.backup_agol.ArcGISRepository")
    @freeze_time("2025-01-14 08:00:00")
    def test_execute_with_no_item_dependencies(self, mock_repository: Mock) -> None:
        frozen_time = datetime.now()
        modified_timestamp = int(frozen_time.timestamp() * 1000)
        source_date_modified = frozen_time.strftime("%Y-%m-%d %H:%M:%S")
        mock_repository.logger = Mock()
        mock_item = Mock(
            title="Solo Item",
            id="solo1",
            type="Feature Layer",
            modified=modified_timestamp,
        )
        mock_repository.get_item.return_value = mock_item
        use_case = AnalyzeItemDependencyUseCase(mock_repository)

        with (
            patch.object(use_case, "_cache_web_content"),
            patch.object(use_case, "_export_to_csv") as mock_export,
        ):
            use_case.execute(["solo1"], "output.csv")

            expected_dependency = ItemDependency(
                source_title="Solo Item",
                source_id="solo1",
                source_type="Feature Layer",
                source_date_modified=source_date_modified,
            )
            mock_export.assert_called_once()
            actual_dependencies = mock_export.call_args[0][0]
            assert len(actual_dependencies) == 1
            assert actual_dependencies[0] == expected_dependency

    @patch("scripts.backup_agol.ArcGISRepository")
    def test_execute_with_item_dependencies(self, mock_repository: Mock) -> None:
        mock_repository.logger = Mock()
        use_case = AnalyzeItemDependencyUseCase(mock_repository)

        expected_dependency = ItemDependency(
            source_title="My Item",
            source_id="webmap1",
            source_type="Web Map",
            source_date_modified="2025-01-14 08:00:00",
            webmap_title="My Webmap",
            webmap_id="webmap123",
            webapp_title="My Webapp",
            webapp_id="webapp123",
        )
        with (
            patch.object(
                use_case, "_find_item_dependencies", return_value=[expected_dependency]
            ),
            patch.object(use_case, "_cache_web_content") as mock_cache,
            patch.object(use_case, "_export_to_csv") as mock_export,
        ):
            use_case.execute(["webmap1"], "output.csv")

            mock_cache.assert_called_once()
            mock_export.assert_called_once()
            actual_dependencies = mock_export.call_args[0][0]
            assert len(actual_dependencies) == 1
            assert actual_dependencies[0] == expected_dependency

    @patch("scripts.backup_agol.ArcGISRepository")
    @freeze_time("2025-01-14 08:00:00")
    def test_find_item_dependencies_with_webapp_deps(
        self, mock_repository: Mock
    ) -> None:
        frozen_time = datetime.now()
        modified_timestamp = int(frozen_time.timestamp() * 1000)
        source_date_modified = frozen_time.strftime("%Y-%m-%d %H:%M:%S")
        mock_repository.logger = Mock()
        use_case = AnalyzeItemDependencyUseCase(mock_repository)
        mock_item = Mock(
            id="webmap1", title="My Item", type="Web Map", modified=modified_timestamp
        )
        use_case.webmaps_cache = {
            "webmap123": {"title": "My Webmap", "data": "webmap1"}
        }
        expected_dependency = ItemDependency(
            source_title="My Item",
            source_id="webmap1",
            source_type="Web Map",
            source_date_modified=source_date_modified,
            webmap_title="My Webmap",
            webmap_id="webmap123",
            webapp_title="My Webapp",
            webapp_id="webapp123",
        )

        with patch.object(
            use_case, "_find_webapp_dependencies", return_value=[expected_dependency]
        ):
            dependencies = use_case._find_item_dependencies(mock_item, None)

            assert len(dependencies) == 1
            assert dependencies[0].webapp_id == "webapp123"

    @patch("scripts.backup_agol.ArcGISRepository")
    def test_find_item_dependencies_with_no_webapp_dependencies(
        self, mock_repository: Mock
    ) -> None:
        mock_repository.logger = Mock()
        use_case = AnalyzeItemDependencyUseCase(mock_repository)
        mock_item = Mock(
            id="webmap1", title="My Item", type="Web Map", modified=1736838000000
        )
        use_case.webmaps_cache = {
            "webmap123": {"title": "My Webmap", "data": "otherdata"}
        }

        with patch.object(use_case, "_find_webapp_dependencies", return_value=[]):
            dependencies = use_case._find_item_dependencies(mock_item, None)

            assert len(dependencies) == 0

    @patch("scripts.backup_agol.ArcGISRepository")
    @freeze_time("2025-01-14 08:00:00")
    def test_find_item_dependencies_for_webmap_with_itemid(
        self, mock_repository: Mock
    ) -> None:
        frozen_time = datetime.now()
        modified_timestamp = int(frozen_time.timestamp() * 1000)
        source_date_modified = frozen_time.strftime("%Y-%m-%d %H:%M:%S")
        mock_repository.logger = Mock()
        use_case = AnalyzeItemDependencyUseCase(mock_repository)
        mock_item = Mock(
            id="webmap1", title="My Item", type="Web Map", modified=modified_timestamp
        )
        use_case.webmaps_cache = {
            "webmap123": {"title": "My Webmap", "data": {"id": "webmap1"}}
        }

        dependencies = use_case._find_item_dependencies(mock_item, None)

        assert dependencies == [
            ItemDependency(
                source_title="My Item",
                source_id="webmap1",
                source_type="Web Map",
                source_date_modified=source_date_modified,
                webmap_title="My Webmap",
                webmap_id="webmap123",
                webapp_title=None,
                webapp_id=None,
            )
        ]

    @patch("scripts.backup_agol.ArcGISRepository")
    @freeze_time("2025-01-14 08:00:00")
    def test_find_item_dependencies_for_webmap_with_item_url(
        self, mock_repository: Mock
    ) -> None:
        frozen_time = datetime.now()
        modified_timestamp = int(frozen_time.timestamp() * 1000)
        source_date_modified = frozen_time.strftime("%Y-%m-%d %H:%M:%S")
        mock_repository.logger = Mock()
        use_case = AnalyzeItemDependencyUseCase(mock_repository)
        mock_item = Mock(
            id="webmap1", title="My Item", type="Web Map", modified=modified_timestamp
        )
        item_url = "https://arcgis/rest/FeatureServer"
        use_case.webmaps_cache = {
            "webmap123": {"title": "My Webmap", "data": {"url": item_url}}
        }

        dependencies = use_case._find_item_dependencies(mock_item, item_url)

        assert dependencies == [
            ItemDependency(
                source_title="My Item",
                source_id="webmap1",
                source_type="Web Map",
                source_date_modified=source_date_modified,
                webmap_title="My Webmap",
                webmap_id="webmap123",
                webapp_title=None,
                webapp_id=None,
            )
        ]

    @patch("scripts.backup_agol.ArcGISRepository")
    @freeze_time("2025-01-14 08:00:00")
    def test_find_webapp_dependencies_with_match(self, mock_repository: Mock) -> None:
        frozen_time = datetime.now()
        modified_timestamp = int(frozen_time.timestamp() * 1000)
        source_date_modified = frozen_time.strftime("%Y-%m-%d %H:%M:%S")
        mock_repository.logger = Mock()
        use_case = AnalyzeItemDependencyUseCase(mock_repository)
        mock_item = Mock(
            id="item1", title="Test Item", type="Web Map", modified=modified_timestamp
        )
        use_case.webapps_cache = {
            "webapp1": {"title": "Test Webapp", "data": "webmap123"}
        }

        dependencies = use_case._find_webapp_dependencies(
            mock_item, "webmap123", "Test Webmap"
        )

        assert len(dependencies) == 1
        assert dependencies[0] == ItemDependency(
            source_title="Test Item",
            source_id="item1",
            source_type="Web Map",
            source_date_modified=source_date_modified,
            webmap_title="Test Webmap",
            webmap_id="webmap123",
            webapp_title="Test Webapp",
            webapp_id="webapp1",
        )

    @patch("scripts.backup_agol.ArcGISRepository")
    def test_find_webapp_dependencies_no_match(self, mock_repository: Mock) -> None:
        mock_repository.logger = Mock()
        use_case = AnalyzeItemDependencyUseCase(mock_repository)
        mock_item = Mock(
            id="item1", title="Test Item", type="Web Map", modified=1736838000000
        )
        use_case.webapps_cache = {
            "webapp1": {"title": "Test Webapp", "data": "different_webmap"}
        }

        dependencies = use_case._find_webapp_dependencies(
            mock_item, "webmap123", "Test Webmap"
        )

        assert len(dependencies) == 0

    @patch("scripts.backup_agol.ArcGISRepository")
    def test_cache_web_content(self, mock_repository: Mock) -> None:
        mock_repository.logger = Mock()
        mock_repository.gis.content.search.return_value = [
            Mock(id="webmap1", title="Web Map 1", get_data=lambda: {"id": "webmap1"}),
            Mock(id="webapp1", title="Web App 1", get_data=lambda: {"id": "webapp1"}),
        ]
        use_case = AnalyzeItemDependencyUseCase(mock_repository)

        use_case._cache_web_content()

        assert "webmap1" in use_case.webmaps_cache
        assert "webapp1" in use_case.webapps_cache

    @patch("scripts.backup_agol.ArcGISRepository")
    def test_cache_web_content_with_error(self, mock_repository: Mock) -> None:
        mock_repository.logger = Mock()
        mock_item = Mock(id="error_item", title="Error Item")
        mock_item.get_data.side_effect = Exception("Failed to get data")
        mock_repository.gis.content.search.return_value = [mock_item]

        use_case = AnalyzeItemDependencyUseCase(mock_repository)
        use_case._cache_web_content()

        mock_repository.logger.warning.assert_called_with(
            "Could not cache Web Application error_item: Failed to get data"
        )

    @patch("scripts.backup_agol.ArcGISRepository")
    @patch("builtins.open")
    def test_export_to_csv(
        self, mock_open: Mock, mock_repository: Mock, tmp_path: Path
    ) -> None:
        output_file = tmp_path / "output.csv"
        mock_repository.logger = Mock()
        dependencies = [
            ItemDependency(
                source_title="Item 1",
                source_id="id1",
                source_type="Web Map",
                source_date_modified="2025-01-14 08:00:00",
                webmap_title="Web Map 1",
                webmap_id="webmap1",
                webapp_title="Web App 1",
                webapp_id="webapp1",
            )
        ]
        use_case = AnalyzeItemDependencyUseCase(mock_repository)

        use_case._export_to_csv(dependencies, str(output_file))

        mock_open.assert_called_once_with(str(output_file), "w", encoding="utf-8")
        mock_repository.logger.info.assert_called_with(
            f"Dependencies exported to {str(output_file)}"
        )

    @patch("scripts.backup_agol.ArcGISRepository")
    @patch("builtins.open")
    def test_export_to_csv_with_error(
        self, mock_open: Mock, mock_repository: Mock
    ) -> None:
        mock_repository.logger = Mock()
        dependencies = [
            ItemDependency(
                source_title="Item 1",
                source_id="id1",
                source_type="Web Map",
                source_date_modified="2025-01-14 08:00:00",
                webmap_title="Web Map 1",
                webmap_id="webmap1",
                webapp_title="Web App 1",
                webapp_id="webapp1",
            )
        ]
        use_case = AnalyzeItemDependencyUseCase(mock_repository)
        mock_open.side_effect = Exception("File error")

        use_case._export_to_csv(dependencies, "output.csv")

        mock_repository.logger.error.assert_called_with(
            "Error exporting to CSV: File error"
        )


class TestBackupItemsUseCase:
    @patch("scripts.backup_agol.os.makedirs")
    @patch("scripts.backup_agol.sanitize_filename")
    @patch("builtins.open")
    @patch("scripts.backup_agol.ArcGISRepository")
    @freeze_time("2012-01-14")
    def test_execute_should_backup_web_content_items(
        self,
        mock_repository: Mock,
        mock_open: Mock,
        mock_sanitize: Mock,
        mock_makedirs: Mock,
        tmpdir: Path,
    ) -> None:
        output_folder = tmpdir / "backups"
        mock_user = Mock()
        mock_user.folders = [{"title": "Folder1"}]
        mock_user.items.return_value = [
            Mock(
                title="Item1", type="Web Experience", get_data=lambda: {"id": "webapp1"}
            ),
            Mock(title="Item2", type="Web Map", get_data=lambda: {"id": "webmap1"}),
        ]
        mock_repository.get_user.return_value = mock_user
        mock_sanitize.return_value = "new_item_name"

        use_case = BackupItemsUseCase(mock_repository)

        with patch.object(
            use_case, "download_item", return_value=None
        ) as mock_download:
            use_case.execute(str(output_folder))

            mock_download.assert_not_called()
            mock_makedirs.assert_called()
            mock_open.assert_called()

    @patch("scripts.backup_agol.os.makedirs")
    @patch("scripts.backup_agol.ArcGISRepository")
    @freeze_time("2012-01-14")
    def test_execute_should_backup_layer_items(
        self,
        mock_repository: Mock,
        mock_makedirs: Mock,
        tmpdir: Path,
    ) -> None:
        output_folder = tmpdir / "backups"
        mock_user = Mock()
        mock_user.folders = [{"title": "Folder1"}]
        mock_user.items.return_value = [
            Mock(title="Item1", type="Feature Service"),
            Mock(title="Item2", type="Feature Service"),
        ]
        mock_repository.get_user.return_value = mock_user
        use_case = BackupItemsUseCase(mock_repository)

        with patch.object(
            use_case, "download_item", return_value=None
        ) as mock_download:
            use_case.execute(str(output_folder))

            mock_download.assert_has_calls(
                [
                    call(
                        f"{str(output_folder)}/Backup_Folder1_20120114",
                        mock_user.items()[0],
                        "geoPackage",
                    ),
                    call(
                        f"{str(output_folder)}/Backup_Folder1_20120114",
                        mock_user.items()[1],
                        "geoPackage",
                    ),
                ]
            )
            mock_makedirs.assert_called()

    @patch("scripts.backup_agol.os.makedirs")
    @patch("builtins.open")
    @patch("scripts.backup_agol.ArcGISRepository")
    @freeze_time("2012-01-14")
    def test_execute_with_specific_folder_should_download_folder_content(
        self,
        mock_repository: Mock,
        mock_open: Mock,
        mock_makedirs: Mock,
        tmpdir: Path,
    ) -> None:
        output_folder = tmpdir / "backups"
        mock_user = Mock()
        mock_user.folders = [{"title": "My folder"}]
        mock_user.items.return_value = [
            Mock(title="Item1", type="Web Map", get_data=lambda: {"id": "webmap1"}),
            Mock(title="Item2", type="Feature Service"),
        ]
        mock_repository.get_user.return_value = mock_user

        use_case = BackupItemsUseCase(mock_repository)
        with patch.object(
            use_case, "download_item", return_value=None
        ) as mock_download:
            use_case.execute(str(output_folder), "My folder")

            mock_user.items.assert_called_with(
                folder={"title": "My folder"}, max_items=1000
            )
            mock_makedirs.assert_called_once_with(
                f"{str(output_folder)}/Backup_My folder_20120114", exist_ok=True
            )
            mock_download.assert_has_calls(
                [
                    call(
                        f"{str(output_folder)}/Backup_My folder_20120114",
                        mock_user.items()[1],
                        "geoPackage",
                    ),
                ]
            )
            mock_open.assert_called()

    @patch("scripts.backup_agol.os.makedirs")
    @patch("builtins.open")
    @patch("scripts.backup_agol.ArcGISRepository")
    @freeze_time("2012-01-14")
    def test_execute_with_nonexistent_folder_should_download_root_content(
        self,
        mock_repository: Mock,
        mock_open: Mock,
        mock_makedirs: Mock,
        tmpdir: Path,
    ) -> None:
        output_folder = tmpdir / "backups"
        mock_user = Mock()
        mock_user.folders = [{"title": "My folder"}]
        mock_user.items.return_value = [
            Mock(title="Item1", type="Web Map", get_data=lambda: {"id": "webmap1"}),
            Mock(title="Item2", type="Feature Service"),
        ]
        mock_repository.get_user.return_value = mock_user

        use_case = BackupItemsUseCase(mock_repository)
        with patch.object(
            use_case, "download_item", return_value=None
        ) as mock_download:
            use_case.execute(str(output_folder), "This folder does not exist")

            mock_user.items.assert_called_with(folder=None, max_items=1000)
            mock_download.assert_has_calls(
                [
                    call(
                        f"{str(output_folder)}/Backup_root_20120114",
                        mock_user.items()[1],
                        "geoPackage",
                    ),
                ]
            )
            mock_makedirs.assert_called_with(
                f"{str(output_folder)}/Backup_root_20120114", exist_ok=True
            )
            mock_open.assert_called()

    @patch("scripts.backup_agol.ArcGISRepository")
    @patch("scripts.backup_agol.Item")
    @freeze_time("2012-01-14")
    def test_download_item_should_export_and_download_features(
        self,
        mock_item_class: Mock,
        mock_repository: Mock,
        tmpdir: Path,
    ) -> None:
        mock_item = Mock(title="My Item", type="Feature Service")
        mock_exported_item = Mock()
        mock_item_class.return_value = mock_item
        mock_item.export.return_value = mock_exported_item
        output_folder = tmpdir / "backups"
        use_case = BackupItemsUseCase(mock_repository)

        use_case.download_item(str(output_folder), mock_item, "geoPackage")

        mock_item.export.assert_called_once_with("My Item_20120114", "geoPackage")
        mock_exported_item.download.assert_called_once_with(output_folder)
        mock_exported_item.delete.assert_called_once()

    @patch("scripts.backup_agol.ArcGISRepository")
    @patch("scripts.backup_agol.Item")
    @freeze_time("2012-01-14")
    def test_download_item_should_download_shapefiles_directly(
        self,
        mock_item_class: Mock,
        mock_repository: Mock,
        tmpdir: Path,
    ) -> None:
        mock_item = Mock(title="My Item", type="Shapefile")
        mock_exported_item = Mock()
        mock_item_class.return_value = mock_item
        mock_item.export.return_value = mock_exported_item
        output_folder = tmpdir / "backups"
        use_case = BackupItemsUseCase(mock_repository)

        use_case.download_item(str(output_folder), mock_item, "geoPackage")

        mock_item.export.assert_not_called()
        mock_item.download.assert_called_once_with(output_folder)
        mock_exported_item.delete.assert_not_called()

    @patch("scripts.backup_agol.ArcGISRepository")
    def test_download_item_with_error(
        self, mock_repository: Mock, tmpdir: Path
    ) -> None:
        mock_item = Mock(title="Error Item", type="Feature Service")
        mock_item.export.side_effect = Exception("Export failed")
        use_case = BackupItemsUseCase(mock_repository)

        use_case.download_item(str(tmpdir), mock_item)

        mock_repository.logger.warning.assert_called_with(
            "An error occurred downloading Error Item: Export failed"
        )


class TestGetFolderStatisticsUseCase:
    @patch("scripts.backup_agol.pd.DataFrame.to_excel")
    @patch("scripts.backup_agol.ArcGISRepository")
    def test_execute_should_generate_statistics(
        self, mock_repository: Mock, mock_to_excel: Mock
    ) -> None:
        mock_user = Mock()
        mock_user.folders = [{"title": "Folder1"}]
        mock_user.items.return_value = [
            Mock(modified=1736838000000, numViews=10),
            Mock(modified=1736838000000, numViews=20),
        ]
        mock_repository.get_user.return_value = mock_user

        use_case = GetFolderStatisticsUseCase(mock_repository)
        use_case.execute("test_user", "output.xlsx")

        mock_to_excel.assert_called_once()

    @patch("scripts.backup_agol.ArcGISRepository")
    def test_execute_should_handle_empty_folders(self, mock_repository: Mock) -> None:
        mock_user = Mock()
        mock_user.folders = []
        mock_repository.get_user.return_value = mock_user

        use_case = GetFolderStatisticsUseCase(mock_repository)
        with patch("scripts.backup_agol.pd.DataFrame", return_value=Mock()):
            use_case.execute("test_user", "output.xlsx")


def test_sanitize_filename_removes_invalid_characters() -> None:
    invalid_filename = 'invalid<>:"/\\|?*filename'
    expected_filename = "invalid_________filename"
    assert sanitize_filename(invalid_filename) == expected_filename


def test_sanitize_filename_keeps_valid_characters() -> None:
    valid_filename = "valid_filename-123"
    assert sanitize_filename(valid_filename) == valid_filename


def test_sanitize_filename_handles_empty_string() -> None:
    empty_filename = ""
    assert sanitize_filename(empty_filename) == ""


@patch("scripts.backup_agol.ArcGISRepository")
@patch("scripts.backup_agol.ArcGISIdentity")
@patch("scripts.backup_agol.AnalyzeItemDependencyUseCase")
@patch("scripts.backup_agol.BackupItemsUseCase")
@patch("scripts.backup_agol.GetFolderStatisticsUseCase")
def test_main_analyze_all_items(
    mock_stats_usecase: Mock,
    mock_backup_usecase: Mock,
    mock_analyze_usecase: Mock,
    mock_identity: Mock,
    mock_repository: Mock,
) -> None:
    mock_analyze_usecase_instance = mock_analyze_usecase.return_value
    mock_repository_instance = mock_repository.return_value
    mock_repository_instance.get_all_item_ids.return_value = ["item1", "item2"]

    main(
        [
            "--action",
            "analyze",
            "--arcgis-website",
            "https://test.com",
            "--arcgis-username",
            "user",
            "--arcgis-password",
            "pass",
            "--output-csv",
            "output.csv",
            "--analyze-all",
        ]
    )

    mock_analyze_usecase_instance.execute.assert_called_once_with(
        ["item1", "item2"], "output.csv"
    )


@patch("scripts.backup_agol.ArcGISRepository")
@patch("scripts.backup_agol.ArcGISIdentity")
@patch("scripts.backup_agol.AnalyzeItemDependencyUseCase")
@patch("scripts.backup_agol.BackupItemsUseCase")
@patch("scripts.backup_agol.GetFolderStatisticsUseCase")
def test_main_analyze_by_folder(
    mock_stats_usecase: Mock,
    mock_backup_usecase: Mock,
    mock_analyze_usecase: Mock,
    mock_identity: Mock,
    mock_repository: Mock,
) -> None:
    mock_analyze_usecase_instance = mock_analyze_usecase.return_value
    mock_repository_instance = mock_repository.return_value
    mock_repository_instance.get_item_ids_by_folder.return_value = ["item1"]

    main(
        [
            "--action",
            "analyze",
            "--arcgis-website",
            "https://test.com",
            "--arcgis-username",
            "user",
            "--arcgis-password",
            "pass",
            "--output-csv",
            "output.csv",
            "--folder-name",
            "Test Folder",
        ]
    )

    mock_analyze_usecase_instance.execute.assert_called_once_with(
        ["item1"], "output.csv"
    )


@patch("scripts.backup_agol.ArcGISRepository")
@patch("scripts.backup_agol.ArcGISIdentity")
@patch("scripts.backup_agol.AnalyzeItemDependencyUseCase")
@patch("scripts.backup_agol.BackupItemsUseCase")
@patch("scripts.backup_agol.GetFolderStatisticsUseCase")
def test_main_analyze_by_item_type(
    mock_stats_usecase: Mock,
    mock_backup_usecase: Mock,
    mock_analyze_usecase: Mock,
    mock_identity: Mock,
    mock_repository: Mock,
) -> None:
    mock_analyze_usecase_instance = mock_analyze_usecase.return_value
    mock_repository_instance = mock_repository.return_value
    mock_repository_instance.get_item_ids_by_type.return_value = ["item1"]

    main(
        [
            "--action",
            "analyze",
            "--arcgis-website",
            "https://test.com",
            "--arcgis-username",
            "user",
            "--arcgis-password",
            "pass",
            "--output-csv",
            "output.csv",
            "--analyzed-item-type",
            "Web Map",
        ]
    )

    mock_analyze_usecase_instance.execute.assert_called_once_with(
        ["item1"], "output.csv"
    )


@patch("scripts.backup_agol.ArcGISRepository")
@patch("scripts.backup_agol.ArcGISIdentity")
@patch("scripts.backup_agol.AnalyzeItemDependencyUseCase")
@patch("scripts.backup_agol.BackupItemsUseCase")
@patch("scripts.backup_agol.GetFolderStatisticsUseCase")
def test_main_no_items_to_analyze(
    mock_stats_usecase: Mock,
    mock_backup_usecase: Mock,
    mock_analyze_usecase: Mock,
    mock_identity: Mock,
    mock_repository: Mock,
    capsys: Any,
) -> None:
    mock_analyze_usecase_instance = mock_analyze_usecase.return_value
    mock_repository_instance = mock_repository.return_value
    mock_repository_instance.get_item_ids_by_type.return_value = []
    mock_repository_instance.get_item_ids_by_folder.return_value = []

    main(
        [
            "--action",
            "analyze",
            "--arcgis-website",
            "https://test.com",
            "--arcgis-username",
            "user",
            "--arcgis-password",
            "pass",
            "--output-csv",
            "output.csv",
        ]
    )

    captured = capsys.readouterr()
    assert "No items to analyze." in captured.out
    mock_analyze_usecase_instance.execute.assert_not_called()


@patch("scripts.backup_agol.ArcGISRepository")
@patch("scripts.backup_agol.ArcGISIdentity")
@patch("scripts.backup_agol.BackupItemsUseCase")
@patch("scripts.backup_agol.GetFolderStatisticsUseCase")
def test_main_missing_output_csv(
    mock_stats_usecase: Mock,
    mock_backup_usecase: Mock,
    mock_identity: Mock,
    mock_repository: Mock,
    capsys: Any,
) -> None:
    mock_analyze_usecase = Mock()
    mock_analyze_usecase_instance = mock_analyze_usecase.return_value

    main(
        [
            "--action",
            "analyze",
            "--arcgis-website",
            "https://test.com",
            "--arcgis-username",
            "user",
            "--arcgis-password",
            "pass",
            "--analyze-all",
        ]
    )

    captured = capsys.readouterr()
    assert "Output CSV file is required." in captured.out
    mock_analyze_usecase_instance.execute.assert_not_called()


@patch("scripts.backup_agol.ArcGISRepository")
@patch("scripts.backup_agol.ArcGISIdentity")
@patch("scripts.backup_agol.AnalyzeItemDependencyUseCase")
@patch("scripts.backup_agol.BackupItemsUseCase")
@patch("scripts.backup_agol.GetFolderStatisticsUseCase")
def test_main_backup(
    mock_stats_usecase: Mock,
    mock_backup_usecase: Mock,
    mock_analyze_usecase: Mock,
    mock_identity: Mock,
    mock_repository: Mock,
) -> None:
    mock_backup_usecase_instance = mock_backup_usecase.return_value

    main(
        [
            "--action",
            "backup",
            "--arcgis-website",
            "https://test.com",
            "--arcgis-username",
            "user",
            "--arcgis-password",
            "pass",
            "--backup-location",
            "/tmp/backup",
        ]
    )

    mock_backup_usecase_instance.execute.assert_called_once_with(
        "/tmp/backup", None, max_items=1000
    )


@patch("scripts.backup_agol.ArcGISRepository")
@patch("scripts.backup_agol.ArcGISIdentity")
@patch("scripts.backup_agol.AnalyzeItemDependencyUseCase")
@patch("scripts.backup_agol.GetFolderStatisticsUseCase")
def test_main_missing_backup_location(
    mock_stats_usecase: Mock,
    mock_analyze_usecase: Mock,
    mock_identity: Mock,
    mock_repository: Mock,
    capsys: Any,
) -> None:
    mock_backup_usecase = Mock()
    mock_backup_usecase_instance = mock_backup_usecase.return_value

    main(
        [
            "--action",
            "backup",
            "--arcgis-website",
            "https://test.com",
            "--arcgis-username",
            "user",
            "--arcgis-password",
            "pass",
        ]
    )

    captured = capsys.readouterr()
    assert "Backup location is required." in captured.out
    mock_backup_usecase_instance.execute.assert_not_called()


@patch("scripts.backup_agol.ArcGISRepository")
@patch("scripts.backup_agol.ArcGISIdentity")
@patch("scripts.backup_agol.AnalyzeItemDependencyUseCase")
@patch("scripts.backup_agol.BackupItemsUseCase")
@patch("scripts.backup_agol.GetFolderStatisticsUseCase")
def test_main_statistics(
    mock_stats_usecase: Mock,
    mock_backup_usecase: Mock,
    mock_analyze_usecase: Mock,
    mock_identity: Mock,
    mock_repository: Mock,
) -> None:
    mock_stats_usecase_instance = mock_stats_usecase.return_value

    main(
        [
            "--action",
            "statistics",
            "--arcgis-website",
            "https://test.com",
            "--arcgis-username",
            "user",
            "--arcgis-password",
            "pass",
            "--output-stats",
            "output.xlsx",
        ]
    )

    mock_stats_usecase_instance.execute.assert_called_once_with(
        "user", "output.xlsx", 1000
    )


@patch("scripts.backup_agol.ArcGISRepository")
@patch("scripts.backup_agol.ArcGISIdentity")
@patch("scripts.backup_agol.AnalyzeItemDependencyUseCase")
@patch("scripts.backup_agol.BackupItemsUseCase")
def test_main_missing_output_stats(
    mock_backup_usecase: Mock,
    mock_analyze_usecase: Mock,
    mock_identity: Mock,
    mock_repository: Mock,
    capsys: Any,
) -> None:
    mock_stats_usecase = Mock()
    mock_stats_usecase_instance = mock_stats_usecase.return_value

    main(
        [
            "--action",
            "statistics",
            "--arcgis-website",
            "https://test.com",
            "--arcgis-username",
            "user",
            "--arcgis-password",
            "pass",
        ]
    )

    captured = capsys.readouterr()
    assert "Output file for statistics is required." in captured.out
    mock_stats_usecase_instance.execute.assert_not_called()


@patch("scripts.backup_agol.ArcGISRepository")
@patch("scripts.backup_agol.ArcGISIdentity")
@patch("scripts.backup_agol.AnalyzeItemDependencyUseCase")
@patch("scripts.backup_agol.BackupItemsUseCase")
def test_main_invalid_action(
    mock_backup_usecase: Mock,
    mock_analyze_usecase: Mock,
    mock_identity: Mock,
    mock_repository: Mock,
    capsys: Any,
) -> None:
    with pytest.raises(
        ValueError, match="Invalid action, choose one of analyze, backup, statistics"
    ):
        main(
            [
                "--action",
                "invalid",
                "--arcgis-website",
                "https://test.com",
                "--arcgis-username",
                "user",
                "--arcgis-password",
                "pass",
            ]
        )


@patch("scripts.backup_agol.main")
def test_execute_script_main(mock_main: Mock) -> None:
    execute_script("__main__")
    mock_main.assert_called_once_with(None)


def test_execute_script_not_main() -> None:
    with patch("scripts.backup_agol.main") as mock_main:
        execute_script("not_main")
        mock_main.assert_not_called()
