import logging.config
import os
import sys

import constants


def get_logger() -> logging.Logger:
    log_path = os.path.abspath(constants.LOGGING_PATH)
    if os.path.exists(log_path):
        try:
            logging.config.fileConfig(constants.LOGGING_PATH)
        except PermissionError:
            logging.error(
                f"Code Error: {constants.CODE_ERR_OUTPUT_FILE_OPEN}, "
                "output file is open"
            )
            sys.exit(constants.CODE_ERR_OUTPUT_FILE_OPEN)

        logger = logging.getLogger()
        return logger
    else:
        logging.error(
            f"Code Error: {constants.CODE_ERR_LOGGING_CONFIG_PATH}, "
            f"{log_path} doesn't exist"
        )
        sys.exit(constants.CODE_ERR_LOGGING_CONFIG_PATH)
