import argparse
import csv
import json
import logging
import os
import re
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional

import pandas as pd
from arcgis.gis import GIS, Item, User


@dataclass(eq=True, frozen=True)
class ArcGISIdentity:
    website: str
    username: str
    password: str


@dataclass
class ItemDependency:
    source_title: str
    source_id: str
    source_type: str
    source_date_modified: Optional[str] = None
    webmap_title: Optional[str] = None
    webmap_id: Optional[str] = None
    webapp_title: Optional[str] = None
    webapp_id: Optional[str] = None

    def to_fields(self) -> Dict[str, str]:
        return {
            "SOURCE NAME": self.source_title,
            "SOURCE ID": self.source_id,
            "SOURCE TYPE": self.source_type,
            "SOURCE DATE MODIFIED": self.source_date_modified or "",
            "WEB MAP NAME": self.webmap_title or "",
            "WEB MAP ID": self.webmap_id or "",
            "WEB APP NAME": self.webapp_title or "",
            "WEB APP ID": self.webapp_id or "",
        }


class ArcGISRepository:
    def __init__(self, arcgis_identity: ArcGISIdentity):
        self.username = arcgis_identity.username
        self.gis = GIS(
            arcgis_identity.website, arcgis_identity.username, arcgis_identity.password
        )
        self.logger = self._setup_logging()

    def _setup_logging(self) -> logging.Logger:
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def get_user(self, username: str) -> User:
        return self.gis.users.get(username)

    def search_items(self, query: str, max_items: int) -> List[Item]:
        return self.gis.content.search(query=query, max_items=max_items)

    def get_item(self, item_id: str) -> Item:
        return self.gis.content.get(item_id)

    def get_all_item_ids(self) -> List[str]:
        return self._get_item_ids()

    def get_item_ids_by_type(self, item_type: str) -> List[str]:
        return self._get_item_ids(item_type=item_type)

    def get_item_ids_by_folder(self, folder_name: str) -> List[str]:
        return self._get_item_ids(folder_name=folder_name)

    def _get_item_ids(
        self,
        item_type: Optional[str] = None,
        folder_name: Optional[str] = None,
        max_items: int = 1000,
    ) -> List[str]:
        if item_type and folder_name:
            raise ValueError(
                "Both item_type and folder_name cannot be specified together."
            )
        user = self.get_user(self.username)
        if folder_name is None:
            folders = [None, *user.folders]
        else:
            folders = [
                next((f for f in user.folders if f["title"] == folder_name), None)
            ]
        item_ids = []
        for folder in folders:
            folder_items = [
                item.id
                for item in user.items(folder=folder, max_items=max_items)
                if (item_type is None or item.type == item_type)
            ]
            item_ids.extend(folder_items)
        return item_ids


class AnalyzeItemDependencyUseCase:
    def __init__(self, repository: ArcGISRepository):
        self.repository = repository
        self.webmaps_cache: Dict[str, Dict[str, Any]] = {}
        self.webapps_cache: Dict[str, Dict[str, Any]] = {}

    def execute(self, item_ids: List[str], output_csv: str) -> None:
        self.repository.logger.info(
            f"Analyzing dependencies for {len(item_ids)} items..."
        )
        self._cache_web_content()
        dependencies = []

        for item_id in item_ids:
            try:
                item = self.repository.get_item(item_id)
                item_url = item.url if hasattr(item, "url") else None

                self.repository.logger.info(f"Processing {item.title}")
                item_deps = self._find_item_dependencies(item, item_url)

                if item_deps:
                    dependencies.extend(item_deps)
                else:
                    dependencies.append(
                        ItemDependency(
                            source_title=item.title,
                            source_id=item.id,
                            source_type=item.type,
                            source_date_modified=datetime.fromtimestamp(
                                item.modified / 1000
                            ).strftime("%Y-%m-%d %H:%M:%S"),
                        )
                    )
                    self.repository.logger.info(
                        f"No dependencies found for {item.title}"
                    )

            except Exception as e:
                self.repository.logger.error(
                    f"Error processing item {item_id}: {str(e)}"
                )

        self._export_to_csv(dependencies, output_csv)

    def _find_item_dependencies(
        self, item: Item, item_url: Optional[str]
    ) -> List[ItemDependency]:
        dependencies = []
        for webmap_id, webmap_info in self.webmaps_cache.items():
            webmap_data = str(webmap_info["data"])
            if (item.id in webmap_data) or (item_url and item_url in webmap_data):
                webapp_deps = self._find_webapp_dependencies(
                    item, webmap_id, webmap_info["title"]
                )

                if webapp_deps:
                    dependencies.extend(webapp_deps)
                else:
                    dependencies.append(
                        ItemDependency(
                            source_title=item.title,
                            source_id=item.id,
                            source_type=item.type,
                            source_date_modified=datetime.fromtimestamp(
                                item.modified / 1000
                            ).strftime("%Y-%m-%d %H:%M:%S"),
                            webmap_title=webmap_info["title"],
                            webmap_id=webmap_id,
                        )
                    )

        return dependencies

    def _find_webapp_dependencies(
        self,
        item: Item,
        webmap_id: str,
        webmap_title: str,
    ) -> List[ItemDependency]:
        dependencies = []

        for webapp_id, webapp_info in self.webapps_cache.items():
            webapp_data = str(webapp_info["data"])

            if webmap_id in webapp_data:
                dependencies.append(
                    ItemDependency(
                        source_title=item.title,
                        source_id=item.id,
                        source_type=item.type,
                        source_date_modified=datetime.fromtimestamp(
                            item.modified / 1000
                        ).strftime("%Y-%m-%d %H:%M:%S"),
                        webmap_title=webmap_title,
                        webmap_id=webmap_id,
                        webapp_title=webapp_info["title"],
                        webapp_id=webapp_id,
                    )
                )

        return dependencies

    def _cache_web_content(self, max_items: int = 1000) -> None:
        self.repository.logger.info("Caching web content...")
        web_item_types = [
            "Web Map",
            "Web Mapping Application",
            "Dashboard",
            "StoryMap",
            "Web Experience",
            "Web Application",
        ]
        for web_item_type in web_item_types:
            web_items = self.repository.gis.content.search(
                query=f"type: {web_item_type}", max_items=max_items
            )
            for web_item in web_items:
                try:
                    if web_item_type == "Web Map":
                        self.webmaps_cache[web_item.id] = {
                            "title": web_item.title,
                            "data": web_item.get_data(),
                        }
                    else:
                        self.webapps_cache[web_item.id] = {
                            "title": web_item.title,
                            "data": web_item.get_data(),
                        }
                except Exception as e:
                    self.repository.logger.warning(
                        f"Could not cache {web_item_type} {web_item.id}: {str(e)}"
                    )

    def _export_to_csv(
        self, dependencies: List[ItemDependency], output_csv: str
    ) -> None:
        fieldnames = [
            "SOURCE NAME",
            "SOURCE ID",
            "SOURCE TYPE",
            "SOURCE DATE MODIFIED",
            "WEB MAP NAME",
            "WEB MAP ID",
            "WEB APP NAME",
            "WEB APP ID",
        ]

        try:
            with open(output_csv, "w", encoding="utf-8") as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()

                for dep in dependencies:
                    writer.writerow(dep.to_fields())

            self.repository.logger.info(f"Dependencies exported to {output_csv}")

        except Exception as e:
            self.repository.logger.error(f"Error exporting to CSV: {str(e)}")


class BackupItemsUseCase:
    def __init__(self, repository: ArcGISRepository):
        self.repository = repository

    def execute(
        self,
        backup_location: str,
        agol_folder_name: Optional[str] = None,
        export_file_format: str = "geoPackage",
        max_items: int = 1000,
    ) -> None:
        user = self.repository.get_user(self.repository.username)
        if agol_folder_name is None:
            folders = [None, *user.folders]
        else:
            folders = [
                next((f for f in user.folders if f["title"] == agol_folder_name), None)
            ]
        for folder in folders:
            folder_title = folder["title"] if folder else "root"
            saved_folder_name = (
                f"Backup_{folder_title}_" f"{datetime.now().strftime('%Y%m%d')}"
            )
            saved_folder_path = os.path.join(backup_location, saved_folder_name)
            os.makedirs(saved_folder_path, exist_ok=True)
            for item in user.items(folder=folder, max_items=max_items):
                if any(
                    x in item.type
                    for x in [
                        "Web Map",
                        "Web Mapping Application",
                        "Dashboard",
                        "StoryMap",
                        "Web Experience",
                        "Web Application",
                    ]
                ):
                    item_title = sanitize_filename(item.title)
                    item_type_prefix = item.type.lower().replace(" ", "_")
                    filename = (
                        f"{item_type_prefix}_{item_title}_"
                        f"{datetime.now().strftime('%Y%m%d')}.json"
                    )
                    with open(os.path.join(saved_folder_path, filename), "w") as f:
                        json.dump(item.get_data(), f, indent=4)
                else:
                    print(f"Downloading {item.title}...")
                    self.download_item(saved_folder_path, item, export_file_format)

    def download_item(
        self, backup_location: str, item: Item, export_format: str = "geoPackage"
    ) -> None:
        try:
            version = datetime.now().strftime("%Y%m%d")
            if "Feature" in item.type:
                result = item.export(f"{item.title}_{version}", export_format)
                result.download(backup_location)
                result.delete()
            else:
                item.download(backup_location)
        except Exception as e:
            self.repository.logger.warning(
                f"An error occurred downloading {item.title}: {e}"
            )


class GetFolderStatisticsUseCase:
    def __init__(self, repository: ArcGISRepository):
        self.repository = repository

    def execute(self, username: str, output_file: str, max_items: int = 1000) -> None:
        user = self.repository.get_user(username)
        folder_data = []
        for folder in user.folders:
            folder_items = user.items(folder=folder, max_items=max_items)
            last_modified_date = datetime.fromtimestamp(
                max([item.modified / 1000 for item in folder_items], default=0)
            )
            number_of_views = sum([item.numViews for item in folder_items])
            folder_data.append(
                {
                    "Dossiers": folder["title"],
                    "Date de dernière activité": last_modified_date,
                    "Nombre de vues total": number_of_views,
                }
            )
        df = pd.DataFrame(folder_data)
        df.to_excel(
            output_file,
            index=False,
            sheet_name=f"Snapshots {datetime.now().strftime('%d-%m-%Y')}",
        )


def sanitize_filename(filename: str) -> str:
    return re.sub(r'[<>:"/\\|?*]', "_", filename)


def main(params: Optional[List[str]] = None) -> None:
    parser = argparse.ArgumentParser(description="ArcGIS Online Management Script")
    parser.add_argument(
        "--action",
        required=True,
        help="Action to perform",
    )
    parser.add_argument(
        "--arcgis-website", "-w", type=str, required=True, help="Website for ArcGIS API"
    )
    parser.add_argument(
        "--arcgis-username",
        "-u",
        type=str,
        required=True,
        help="Username for ArcGIS API",
    )
    parser.add_argument(
        "--arcgis-password",
        "-p",
        type=str,
        required=True,
        help="Password for ArcGIS API",
    )
    parser.add_argument("--backup-location", type=str, help="Location to store backups")
    parser.add_argument("--output-csv", type=str, help="Output CSV file for analysis")
    parser.add_argument("--output-stats", type=str, help="Output file for statistics")
    parser.add_argument(
        "--folder-name", type=str, help="Folder name to analyze or backup"
    )
    parser.add_argument(
        "--analyzed-item-type", type=str, help="Type of items to analyze"
    )
    parser.add_argument("--analyze-all", action="store_true", help="analyze all items")
    parser.add_argument("--max-items", type=int, default=1000, help="Max items")
    args = parser.parse_args(params)

    arcgis_identity = ArcGISIdentity(
        args.arcgis_website, args.arcgis_username, args.arcgis_password
    )
    repository = ArcGISRepository(arcgis_identity)
    analyze_item_dependencies_use_case = AnalyzeItemDependencyUseCase(repository)
    backup_items_use_case = BackupItemsUseCase(repository)
    get_folder_statistics_use_case = GetFolderStatisticsUseCase(repository)

    if args.action == "analyze":
        if not args.output_csv:
            print("Output CSV file is required.")
            return
        if args.analyze_all:
            item_ids = repository.get_all_item_ids()
        elif args.folder_name:
            item_ids = repository.get_item_ids_by_folder(args.folder_name)
        elif args.analyzed_item_type:
            item_ids = repository.get_item_ids_by_type(args.analyzed_item_type)
        else:
            print("No items to analyze.")
            return
        analyze_item_dependencies_use_case.execute(item_ids, args.output_csv)
    elif args.action == "backup":
        if not args.backup_location:
            print("Backup location is required.")
            return
        folder_name = None if not args.folder_name else args.folder_name
        backup_items_use_case.execute(
            args.backup_location, folder_name, max_items=args.max_items
        )
    elif args.action == "statistics":
        if not args.output_stats:
            print("Output file for statistics is required.")
            return
        get_folder_statistics_use_case.execute(
            args.arcgis_username, args.output_stats, args.max_items
        )
    else:
        raise ValueError("Invalid action, choose one of analyze, backup, statistics")


def execute_script(name: str, params: Optional[List[str]] = None) -> None:
    if name == "__main__":
        main(params)


execute_script(__name__)
