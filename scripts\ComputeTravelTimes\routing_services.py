import logging
from abc import ABC, abstractmethod
from datetime import datetime as dt
from typing import Any, Dict, List, Tuple, Union

import requests

logger = logging.getLogger(__name__)


class RoutingApi(ABC):
    def __init__(self, api_config: Dict[str, Any]):
        self.api_config = api_config

    @abstractmethod
    def get_travel_times(
        self,
        origin: Tuple[float, float],
        destination: Tuple[float, float],
        departure_time: str,
    ) -> Dict[str, Any]:
        pass


class GoogleApi(RoutingApi):

    def __init__(self, api_config: Dict[str, Any]):
        super().__init__(api_config)

    def get_travel_times(
        self,
        origin: Tuple[float, float],
        destination: Tuple[float, float],
        departure_time: Union[str, int],
    ) -> Dict[str, Any]:
        url = f'{self.api_config["url"]}/directions/json'
        headers = {"Content-Type": "application/json"}

        payload = {
            "origin": f"{origin[0]},{origin[1]}",
            "destination": f"{destination[0]},{destination[1]}",
            "key": self.api_config["apiKey"],
            "mode": self.api_config["mode"],
            "departure_time": departure_time,
            "sensors": "true",
            "language": "fr",
        }
        if departure_time != "now":
            if isinstance(departure_time, str):
                departure_time = int(
                    dt.strptime(departure_time, "%Y-%m-%dT%H:%M:%S").timestamp()
                )
            payload["departure_time"] = departure_time
        google_traffic_models = ["best_guess", "optimistic", "pessimistic"]
        dict_response = {}
        for traffic_model in google_traffic_models:
            payload["traffic_model"] = traffic_model
            response = requests.get(url, params=payload, headers=headers)
            if response.status_code == 200:
                dict_eval = eval(response.text)["routes"][0]["legs"][0]
                dict_response["distance"] = dict_eval["distance"]["value"]
                dict_response[f"duration_{traffic_model}"] = dict_eval["duration"][
                    "value"
                ]
                dict_response["baseduration"] = dict_eval["duration"]["value"]
                logger.info(f"Request Succeeded - Code: {response.status_code}")
            else:
                logger.info(
                    f"Request failed - Code: {response.status_code}"
                    f"- Error: {response.text}"
                )
        return dict_response


class TomtomApi(RoutingApi):
    def __init__(self, api_config: Dict[str, Any]):
        super().__init__(api_config)

    def get_travel_times(
        self,
        origin: Tuple[float, float],
        destination: Tuple[float, float],
        departure_time: str,
    ) -> Dict[str, Any]:
        url = self.api_config["url"]
        headers = {"Content-Type": "application/json"}
        payload = {
            "key": self.api_config["apiKey"],
            "traffic": self.api_config["trafic"],
            "travelMode": self.api_config["mode"],
            "computeTravelTimeFor": self.api_config["computeTravelTimeFor"],
            "departAt": departure_time,
        }
        payload_str = (
            f"{origin[0]},{origin[1]}:{destination[0]},{destination[1]}"
            f"/json?{'&'.join([f'{k}={payload[k]}' for k in payload.keys()])}"
        )
        url = url + payload_str
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            logger.info(f"Request Succeeded - Code: {response.status_code}")
            dict_response = eval(response.text)["routes"][0]["summary"]
            dict_response["distance"] = dict_response.pop("lengthInMeters")
            dict_response.pop("trafficDelayInSeconds")
            dict_response.pop("trafficLengthInMeters")

            dict_response["notraffic"] = dict_response.pop(
                "noTrafficTravelTimeInSeconds"
            )
            dict_response["historictraffic"] = dict_response.pop(
                "historicTrafficTravelTimeInSeconds"
            )
            dict_response["livetrafficincidents"] = dict_response.pop(
                "liveTrafficIncidentsTravelTimeInSeconds"
            )
        return dict_response


class HereApi(RoutingApi):
    def __init__(self, api_config: Dict[str, Any]):
        super().__init__(api_config)

    def get_travel_times(
        self,
        origin: Tuple[float, float],
        destination: Tuple[float, float],
        departure_time: str,
    ) -> Dict[str, Any]:
        url = self.api_config["url"]
        headers = {"Content-Type": "application/json"}
        payload = {
            "apiKey": self.api_config["apiKey"],
            "transportMode": self.api_config["mode"],
            "departureTime": departure_time,
            "trafic": "enabled",
            "origin": f"{origin[0]},{origin[1]}",
            "destination": f"{destination[0]},{destination[1]}",
            "return": self.api_config["return"],
            "spans": self.api_config["spans"],
        }
        payload_str = f"?{'&'.join([f'{k}={payload[k]}' for k in payload.keys()])}"
        url = url + payload_str
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            logger.info(f"Request Succeeded - Code: {response.status_code}")
            dict_response = eval(response.text)["routes"][0]["sections"][0]["summary"]
            dict_response["distance"] = dict_response.pop("length")
        else:
            logger.info(
                f"Request failed - Code: {response.status_code}"
                f"- Error: {response.text}"
            )
        return dict_response


class GiscoreApi(RoutingApi):
    def __init__(self, api_config: Dict[str, Any]):
        super().__init__(api_config)

    def _get_link_dirs(
        self, origin: Tuple[float, float], destination: Tuple[float, float]
    ) -> List[str]:
        # Need to be implemented??

        return []

    def _login(self) -> bool:
        url = self.api_config["url"] + "/login"
        headers = {"Content-Type": "application/json"}
        payload = {
            "username": self.api_config["username"],
            "password": self.api_config["password"],
        }
        response = requests.post(url, headers=headers, json=payload)
        if response.status_code == 200:
            logger.info(
                "Login Succeeded - Code: "
                + str(response.status_code)
                + " - Response: "
                + response.text
            )
            self.cookie = response.headers["set-cookie"]
            return True
        else:
            logger.info(
                f"Login failed - Code: {response.status_code}"
                f"- Error: {response.text}"
            )
            return False

    def get_travel_times(
        self,
        origin: Tuple[float, float],
        destination: Tuple[float, float],
        departure_time: str = "",
    ) -> Dict[str, Any]:

        # TODO: Need to be implemented??
        return {}


class RoutingService:
    def __init__(self, routing_apis: List[RoutingApi]):
        self.routing_apis = routing_apis

    def get_travel_times(
        self,
        origin: Tuple[float, float],
        destination: Tuple[float, float],
        departure_time: str,
    ) -> Dict[str, Any]:
        travel_times = {}
        for api in self.routing_apis:
            api_name = api.__class__.__name__
            try:
                travel_times[api_name] = api.get_travel_times(
                    origin, destination, departure_time
                )
            except Exception as e:
                logger.exception(f"Error while calling {api_name} API: {str(e)}")
        return travel_times
