#!/bin/bash

failures=0
trap 'failures=$((failures+1))' ERR

isort . --check --diff
black --check --diff -t py310 --exclude="venv|alembic|.eggs" .
mypy
flake8
pytest --cov --cov-branch --cov-fail-under=100 --cov-report=term-missing:skip-covered

if ((failures == 0)); then
  echo -e "\e[0;32m $(tput bold) Success $(tput sgr0) \e[0m"
else
  echo -e "\e[0;31m $(tput bold)${failures} checks failed !! $(tput sgr0)\e[0m"
  exit 1
fi
