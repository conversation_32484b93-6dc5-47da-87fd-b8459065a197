from typing import Any

import geopandas
import pandas as pd
import pytest
from shapely.geometry import LineString, Point

from scripts.merge_fcd_with_network import (
    EpochType,
    compute_peak_means,
    execute_script,
    find_all_epochs,
    get_epoch_column,
    is_within_peak_hours,
    translate_epoch_time,
)


class TestTranslateEpochTime:
    @pytest.mark.parametrize(
        "epoch_type, epoch, expected",
        [
            (EpochType.EPOCH_15MIN, 0, "00H00"),
            (EpochType.EPOCH_15MIN, 1, "00H15"),
            (EpochType.EPOCH_15MIN, 4, "01H00"),
            (EpochType.EPOCH_5MIN, 0, "00H00"),
            (EpochType.EPOCH_5MIN, 1, "00H05"),
            (EpochType.EPOCH_5MIN, 12, "01H00"),
            (EpochType.EPOCH_60MIN, 0, "0H"),
            (EpochType.EPOCH_60MIN, 1, "1H"),
        ],
    )
    def test_translate_epoch_time(
        self, epoch_type: EpochType, epoch: int, expected: str
    ) -> None:
        assert translate_epoch_time(epoch_type, epoch) == expected

    def test_translate_epoch_time_raises_error(self) -> None:
        with pytest.raises(ValueError, match="Unsupported epoch column type"):
            translate_epoch_time(EpochType.from_string("EPOCH-30MIN"), 0)

    @pytest.mark.parametrize(
        "epoch_type, expected",
        [
            (EpochType.EPOCH_15MIN, 15),
            (EpochType.EPOCH_5MIN, 5),
            (EpochType.EPOCH_60MIN, 60),
            (EpochType.UNKNOWN, -1),
        ],
    )
    def test_get_epoch_duration(self, epoch_type: EpochType, expected: int) -> None:
        assert epoch_type.get_epoch_duration() == expected


class TestGetEpochColumn:
    def test_get_epoch_column_finds_it(self) -> None:
        data = {"EPOCH-60MIN": "value", "other_column": "value"}
        epoch_column = get_epoch_column(data)
        assert epoch_column is not None
        assert epoch_column.get_code() == "EPOCH-60MIN"

    def test_get_epoch_column_not_found(self) -> None:
        data = {"other_column": "value"}
        epoch_column = get_epoch_column(data)
        assert epoch_column == EpochType.UNKNOWN


class TestFindAllEpochs:
    def test_find_all_epochs_with_no_epoch_column(self, tmp_path: Any) -> None:
        fcd_file = tmp_path / "fcd.csv"
        fcd_file.write_text(
            "LINK-DIR,DATE-TIME,LENGTH,FREEFLOW,SPDLIMIT,COUNT,MEAN,MIN,MAX,GAPFILL\n"
            "0B,2022-09-03 16:00,37,10.8,50,56,25.3,11,11,Y\n"
        )
        with pytest.raises(ValueError, match="No epoch column found in the data"):
            find_all_epochs(str(fcd_file))

    def test_find_all_epochs_at_60min(self, tmp_path: Any) -> None:
        fcd_file = tmp_path / "fcd.csv"
        fcd_file.write_text(
            "LINK-DIR,DATE-TIME,EPOCH-60MIN,LENGTH,FREEFLOW,SPDLIMIT,COUNT,MEAN,MIN,MAX,GAPFILL\n"  # noqa: E501
            "0B,2022-09-03 16:00,16,37,10.8,50,56,25.3,11,11,Y\n"
            "0B,2022-09-03 17:00,17,37,10.8,50,10,30,11,11,Y\n"
            "0B,2022-09-04 17:00,17,37,10.8,50,22,40,11,11,Y\n"
        )
        epoch_column, all_epochs = find_all_epochs(str(fcd_file))
        assert epoch_column is not None
        assert epoch_column.get_code() == "EPOCH-60MIN"
        assert all_epochs == [16, 17]


class TestIsWithinPeakHours:
    @pytest.mark.parametrize(
        "epoch, peak_start, peak_end, epoch_duration, expected",
        [
            (16, 7, 8, 60, False),
            (7, 7, 8, 60, True),
            (8, 7, 8, 60, False),
            (7, 7, 8, 15, False),
            (30, 7, 8, 15, True),
        ],
    )
    def test_is_within_peak_hours(
        self,
        epoch: int,
        peak_start: int,
        peak_end: int,
        epoch_duration: int,
        expected: bool,
    ) -> None:
        assert (
            is_within_peak_hours(epoch, peak_start, peak_end, epoch_duration)
            == expected
        )


class TestComputePeakMeans:
    def test_compute_peak_means(self) -> None:
        data = {
            "MEAN00H00": [10, 20, 30],
            "MEAN00H15": [15, 25, 35],
            "MEAN01H00": [20, 30, 40],
        }
        df = pd.DataFrame(data)
        epoch_column = EpochType.EPOCH_15MIN
        all_epochs = [0, 1, 4]
        peak_start = 0
        peak_end = 1
        epoch_duration = 15

        result = compute_peak_means(
            df, epoch_column, all_epochs, peak_start, peak_end, epoch_duration
        )

        expected = pd.Series([12.5, 22.5, 32.5])
        pd.testing.assert_series_equal(result, expected)


class TestMain:
    def test_execute_script_does_nothing_if_not_main(self) -> None:
        execute_script("__aint_no_main__")

    def test_execute_script_fails_without_args(self) -> None:
        with pytest.raises(SystemExit) as excinfo:
            execute_script("__main__")

        assert excinfo.value.code == 2

    def test_execute_script_fails_if_file_does_not_exist(self) -> None:
        args = ["shapefile.shp", "fcd.csv"]

        with pytest.raises(SystemExit) as excinfo:
            execute_script("__main__", args)

        assert excinfo.value.code == 2

    def test_execute_script_merges_shapefile_and_fcd(self, tmp_path: Any) -> None:
        shape_file = tmp_path / "shapefile.shp"
        fcd_file = tmp_path / "fcd.csv"
        geo_dataframe = geopandas.GeoDataFrame(
            {
                "TARGET_FID": [123],
                "LINK_ID": [0],
                "ST_NAME": [641861],
                "FEAT_ID": [35321],
                "FUNC_CLASS": [3],
                "SPEED_CAT": [4],
                "FR_SPD_LIM": [80],
                "TO_SPD_LIM": [80],
                "TO_LANES": [1],
                "FROM_LANES": [1],
                "DIVIDER": ["N"],
                "DIR_TRAVEL": ["B"],
            },
            crs="EPSG:4326",
            geometry=[Point(45.5, 5.0)],
        )
        geo_dataframe.to_file(str(shape_file), driver="ESRI Shapefile")
        fcd_file.write_text(
            "LINK-DIR,DATE-TIME,EPOCH-60MIN,LENGTH,FREEFLOW,"
            "SPDLIMIT,COUNT,MEAN,MIN,MAX,GAPFILL\n"
            "0B,2022-09-03 16:00,16,37,10.8,50,56,25.3,11,11,Y\n"
            "0B,2022-09-03 17:00,17,37,10.8,50,10,30,11,11,Y\n"
            "0B,2022-09-04 17:00,17,37,10.8,50,22,40,11,11,Y\n"
        )
        args = [str(shape_file), str(fcd_file), "-o", str(tmp_path)]

        execute_script("__main__", args)

        out_folder = tmp_path / "joined_network_with_fcd" / "shapefile" / "fcd"
        file_made = out_folder / "speed_data.shp"
        produced_dataframe = geopandas.read_file(str(file_made))
        assert list(produced_dataframe["LINK_ID"]) == [0]
        assert list(produced_dataframe["COUNT16H"]) == [56]
        assert list(produced_dataframe["MEAN16H"]) == [25.3]
        assert list(produced_dataframe["COUNT17H"]) == [32]
        assert list(produced_dataframe["MEAN17H"]) == [35.0]

    def test_execute_script_merges_shapefile_and_fcd_with_preprocessing(
        self, tmp_path: Any
    ) -> None:
        shape_file = tmp_path / "shapefile.shp"
        fcd_file = tmp_path / "fcd.csv"
        geo_dataframe = geopandas.GeoDataFrame(
            {
                "TARGET_FID": [123],
                "LINK_ID": [0],
                "ST_NAME": [641861],
                "FEAT_ID": [35321],
                "FUNC_CLASS": [3],
                "SPEED_CAT": [4],
                "FR_SPD_LIM": [80],
                "TO_SPD_LIM": [80],
                "TO_LANES": [1],
                "FROM_LANES": [1],
                "DIVIDER": ["N"],
                "DIR_TRAVEL": ["B"],
            },
            crs="EPSG:4326",
            geometry=[LineString([(45.5, 5.0), (45.6, 5.1)])],
        )
        geo_dataframe.to_file(str(shape_file), driver="ESRI Shapefile")
        fcd_file.write_text(
            "LINK-DIR,DATE-TIME,EPOCH-60MIN,LENGTH,FREEFLOW,"
            "SPDLIMIT,COUNT,MEAN,MIN,MAX,GAPFILL\n"
            "0F,2022-09-03 16:00,16,37,10.8,50,56,25.3,11,11,Y\n"
            "0T,2022-09-03 16:00,16,37,10.8,50,40,25.3,11,11,Y\n"
            "0F,2022-09-03 17:00,17,37,10.8,50,10,30,11,11,Y\n"
            "0T,2022-09-04 17:00,17,37,10.8,50,22,40,11,11,Y\n"
        )
        args = [str(shape_file), str(fcd_file), "-o", str(tmp_path), "-p"]

        execute_script("__main__", args)

        out_folder = tmp_path / "joined_network_with_fcd" / "shapefile" / "fcd"
        file_made = out_folder / "speed_data.shp"
        produced_dataframe = geopandas.read_file(str(file_made))
        assert list(produced_dataframe["LINK_DIR"]) == ["0F", "0T"]
        assert list(produced_dataframe["COUNT16H"]) == [56.0, 40.0]
        assert list(produced_dataframe["MEAN16H"]) == [25.3, 25.3]
        assert list(produced_dataframe["COUNT17H"]) == [10.0, 22.0]
        assert list(produced_dataframe["MEAN17H"]) == [30.0, 40.0]
