from functools import wraps
from time import time
from typing import Any, Callable, Dict, Iterable, List, Tuple

from rich.progress import BarColumn, Progress, TextColumn, TimeRemainingColumn
from rich.table import Table

progress = Progress(
    TextColumn("[bold blue]{task.fields[filename]}", justify="right"),
    BarColumn(bar_width=None),
    "[progress.percentage]{task.percentage:>3.1f}%",
    "•",
    TimeRemainingColumn(),
)


class TTEProgress(Progress):

    def get_renderables(self) -> Iterable[Table]:
        for task in self.tasks:
            if task.fields.get("progress_type") == "origin":
                total = task.total if task.total is not None else 0
                self.columns = (
                    TextColumn(
                        f"[magenta]Processing [bold green]{total} origin"
                        f"{'s' if float(total) > 1.0 else ''}",
                        justify="right",
                    ),
                    BarColumn(bar_width=50),
                    "[progress.percentage]{task.percentage:>3.1f}%",
                    "•",
                    TextColumn(
                        f"[green]{task.completed} of {total} completed origins",
                        justify="right",
                    ),
                )
            if task.fields.get("progress_type") == "destination":
                self.columns = (
                    TextColumn("[blue]{task.fields[origin]}", justify="right"),
                    BarColumn(bar_width=50),
                    "[progress.percentage]{task.percentage:>3.1f}%",
                    "•",
                    TextColumn(
                        f"[green]{task.completed} of "
                        f"{total} completed destinations",
                        justify="right",
                    ),
                )
            yield self.make_tasks_table([task])


def measure_time(func: Callable) -> Any:
    @wraps(func)
    def time_it(*args: Any, **kwargs: Any) -> Any:
        start = int(round(time() * 1000))
        try:
            return func(*args, **kwargs)
        finally:
            end_ = int(round(time() * 1000)) - start
            print(
                f"Total execution time for "
                f"the {time_it.__doc__} is: {end_ if end_ > 0 else 0} ms"
            )

    return time_it


def flatten_dict(d: Dict, parent_key: str = "", sep: str = "_") -> Dict[str, Dict]:
    items: List[Tuple[str, Dict]] = []
    for k, v in d.items():
        new_key: str = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)
