MobiliTools
###########

🚈 🚲 Tools for mobility ;) 🚌 🚋
'''''''''''''''''''''''''''''''''''

Repository content
==================

- mobilitools

  - scripts: single file python executables (cf #Scripts)

Setup
=====

Run
---

#) Setup your virtualenv (python 3.10)
#) Install dependencies `pip install -r requirements.txt`

Develop
-------

Install pre-commit hook::

  cd mobilitools/.git/hooks
  ln -s ../../hooks/pre-commit

Setup your git config::

  git config user.email "<EMAIL>"
  git config user.name "Your Name"


Scripts
=======

merge_fcd_with_network
  This tools merges Here floating car data (CSV) with Here network (shapefile) into
  a single easy-to-use shapefile where each edge contains consolidated speed data.

  Speed data is consolidated by hour meaning that each "epoch" contained in the source
  fcd extract is kept in the end, and the data for each day is either summed or averaged
  depending on what makes sense (e.g. COUNT is summed, MEAN is averaged).

  It means that in order to obtain the typical speed data on a working week day the initial
  speed data could for example contain speed data from monday to friday on different weeks.
  Ultimately what speed data is fed to the script is up to the engineer and depends on their
  specific modeling needs.

run_match_probes
  This tool reconstructs the path of a vehicle from a set of probe points. It uses the
  HERE routematching API to match the probe points to the road network.
  For more information on the HERE Route matching API, see
  https://developer.here.com/documentation/route-matching/api-reference.html

run_compute_proportions
  This tool computes the proportion of vehicles movement at an intersection.
  It uses the HERE route matched probe data and the HERE network to compute the
  proportion of vehicles that take each branch of an intersection.
  The segments link_id that we use to compute the proportion are recorded in a json file
  that is provided as an input to the script. The json file should have the following format:
  {
    {
    "all_origin_links": {
      [origin_link_id_1, origin_link_id_2, ...],
      [origin_2_link_id_1, origin_2_link_id_2, ...],
      ...
    },
    "possible_links_mouvements": {
      [destination_link_id_1, destination_link_id_2, ...],
      [destination_2_link_id_1, destination_2_link_id_2, ...],
      ...
    }
  }

ComputeTravelTimes
  This tool allows to do travel time computation using different routing services: HERE, Google, TomTom.
  The codebase is legacy and a refactor of the GISCoreAPIRouting repository previously used for FIFA.
  The different APIs specific parameters are encoded in a config file provided in the CLI to run the main script `travel_time_routing_services.py`
  The script will output a CSV file with the travel time for each route.
  The required cli parameters are the following:
  - `--config` : path to the config file
  - `start_times` : start times for the travel time computation
  - `end_times` : end times for the travel time computation
  There are also optional parameters:
  - `freq`: frequency between start and end times
  - `config_updates`: optional dict to update the config file with the provided parameters
  - `--use_real_time`: flag to use now as departure time for the routing services

SwisscomAPIAccess
  This tool allows to request origin-destination trafic flows between locations in Switzerland from the Swisscom mobile network.
  The tool must be executed with a JSON config file: `python SwisscomAPIAccessTool.py <config_file_path>`.
  A user guide is available in the `SwisscomAPIAccess/Documentation` directory. Sample config files are shown in the `SwisscomAPIAccess/Config` directory.
  For more information, see https://citecch.sharepoint.com/sites/ProjetdestageYZ/Documents%20partages/Forms/AllItems.aspx?id=%2Fsites%2FProjetdestageYZ%2FDocuments%20partages%2FGeneral%2FTechnique%2FEtude%20donn%C3%A9es%20mobiles&viewid=5a5cb379%2Db0b5%2D45a8%2Db824%2D1dce5ba23a9d

GenerationFluxODFast
  This tool allows to perform origin-destination flow computation from HERE Probe Data in a much more efficient way than ArcGIS geoprocessing tools.
  The tool in a standalone script in which the parameters are hard-coded (lines 8-24).
  This script comes from https://citecch.sharepoint.com/sites/ProjetdestageYZ/Documents%20partages/Forms/AllItems.aspx?id=%2Fsites%2FProjetdestageYZ%2FDocuments%20partages%2FGeneral%2FTechnique%2FUtilitaires%2FGenerationFluxOD%5FFast&viewid=5a5cb379%2Db0b5%2D45a8%2Db824%2D1dce5ba23a9d

select_link_id_from_sample
  This tool allows to select data from a CSV file containing HERE data based on the LINK_ID or LINK_DIR field.
  The required cli parameters are the following:
  - path to the CSV file containing the links to select
  - path to the CSV file contaiing the data to filter
  - path to the output CSV file where the filtered data will be written
  There are also optional parameters:
  - name of the LINK_ID field in the CSV file containing the links to select (default="LINK_ID")
  - name of the LINK_ID field in the CSV file containing the data to filter (default="LINK_ID")
  This script comes from https://citecch.sharepoint.com/sites/ProjetdestageYZ/Documents%20partages/Forms/AllItems.aspx?id=%2Fsites%2FProjetdestageYZ%2FDocuments%20partages%2FGeneral%2FTechnique%2FUtilitaires%2FSelectLinkIDFromSample&viewid=5a5cb379%2Db0b5%2D45a8%2Db824%2D1dce5ba23a9d


backup_agol
  This tool implements a solution for managing ArcGIS Online content with three main features:
  - Analyze item dependencies and relationships
      Ex: `python scripts/backup_agol.py --action analyze --arcgis-website https://www.arcgis.com --arcgis-username user --arcgis-password pass --output-csv dependencies.csv --analyze-all`
  - Backup web item content and feature services
      Ex: `python scripts/backup_agol.py --action backup --arcgis-website https://www.arcgis.com --arcgis-username user --arcgis-password pass --backup-location ./backups`
  - Generate folder statistics
      Ex: `python scripts/backup_agol.py --action statistics --arcgis-website https://www.arcgis.com --arcgis-username user --arcgis-password pass --output-stats stats.json`
  For both analyze and backup actions, you can specify the folder from the ArcGIS Online portal to analyze/backup with the `--folder-name` parameter.
  We can find the archiving and restoring process described in this loop page: `Guide d'archivage <https://citecch.sharepoint.com/:fl:/g/contentstorage/CSP_7367e8eb-e6fe-41ba-b114-91410df2d6e4/EVbOlWahmtFFlxUgvvBxZiQB1sUlJV5X1LUYM1uJYHJhGQ?e=HoUdaP&nav=cz0lMkZjb250ZW50c3RvcmFnZSUyRkNTUF83MzY3ZThlYi1lNmZlLTQxYmEtYjExNC05MTQxMGRmMmQ2ZTQmZD1iJTIxNi1obmNfN211a0d4RkpGQkRmTFc1Q0N3b0JGUkk0WkhnQVBtSFZaX0xHZEVDclFYODB4VlRKeEdYczlFbml4RiZmPTAxQ0JBUkRCMldaMktXTklNMjJGQ1pPRkpBWDNZSENaUkUmYz0lMkYmYT1Mb29wQXBwJng9JTdCJTIydyUyMiUzQSUyMlQwUlRVSHhqYVhSbFkyTm9Mbk5vWVhKbGNHOXBiblF1WTI5dGZHSWhZekpZVDJVelVXbFRhMjFYTWxFMlJtb3RhemRXVVRORVowOHdXbFJVZEVSMFFUSkNiSEpKTm1acU1FUm1iRjl0UTA1VmRGTlpjblJ2ZG1SUE1sRndTSHd3TVU1U1ZVUkpWVUZXUlU1R1Z6WlZVMDFMU2tSWlZqUlBVMWRaTTFKRFdqUlElMjIlMkMlMjJpJTIyJTNBJTIyYzA1YzJmZGMtMzIyZi00MTQ5LWE0N2MtYzZiODNkNjkzMWFhJTIyJTdE>`_