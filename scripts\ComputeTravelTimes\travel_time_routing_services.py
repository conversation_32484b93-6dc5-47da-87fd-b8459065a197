import argparse
import json
import logging
import os
import sys
from datetime import datetime as dt
from typing import Any, Dict, List, Optional, Tuple

import pandas as pd
from routing_services import G<PERSON>reApi, GoogleApi, HereApi, RoutingService, TomtomApi
from utils import flatten_dict, measure_time

logger = logging.getLogger(__name__)


class GISCoreAPIRouting:

    def __init__(
        self, config_file_path: str, departure_times: Optional[List[str]] = None
    ):
        with open(config_file_path, encoding="utf-8") as json_data:
            self.json_file = json.load(json_data)
        self.input_csv_path = self.json_file["global_parameters"]["input_csv_path"]
        self.output_csv_path = self.json_file["global_parameters"]["output_csv_path"]
        self.max_origin = self.json_file["global_parameters"]["max_origin"]
        self.max_destination = self.json_file["global_parameters"]["max_destination"]
        self.x = self.json_file["global_parameters"]["x"]
        self.y = self.json_file["global_parameters"]["y"]
        self.location_id = self.json_file["global_parameters"]["location_id"]
        self.list_departure_time = self.json_file["global_parameters"][
            "list_departure_time"
        ]
        self.list_departure_time = departure_times or self.list_departure_time
        self.launch_time = dt.now().strftime("%Y-%m-%dT%H:%M:%S")
        self.routing_service = self._create_routing_service()

    def _create_routing_service(self) -> RoutingService:
        routing_apis = []
        if "google" in self.json_file and self.json_file["google"]["use"]:
            routing_apis.append(GoogleApi(self.json_file["google"]["parameters"]))
        if "tomtom" in self.json_file and self.json_file["tomtom"]["use"]:
            routing_apis.append(TomtomApi(self.json_file["tomtom"]["parameters"]))
        if (
            "giscore_v1.0.0" in self.json_file
            and self.json_file["giscore_v1.0.0"]["use"]
        ):
            routing_apis.append(
                GiscoreApi(self.json_file["giscore_v1.0.0"]["parameters"])
            )
        if "here_v8" in self.json_file and self.json_file["here_v8"]["use"]:
            routing_apis.append(HereApi(self.json_file["here_v8"]["parameters"]))
        return RoutingService(routing_apis)

    def _get_travel_times(
        self,
        origin: Tuple[float, float],
        destination: Tuple[float, float],
        departure_time: str,
    ) -> Dict[str, Any]:
        return self.routing_service.get_travel_times(
            origin, destination, departure_time
        )

    def _get_travel_times_for_pair(
        self, origin_index: int, destination_index: int, input_df: pd.DataFrame
    ) -> Dict[str, Any]:
        origin = input_df.loc[origin_index, [self.y, self.x]]
        from_id = input_df.loc[origin_index, self.location_id]
        destination = input_df.loc[destination_index, [self.y, self.x]]
        to_id = input_df.loc[destination_index, self.location_id]
        departure_times = self.list_departure_time or [self.launch_time]
        travel_times = {}
        for departure_time in departure_times:
            travel_times[f"{origin_index+1}_{destination_index+1}_{departure_time}"] = {
                "origin": origin_index + 1,
                "destination": destination_index + 1,
                "from_id": from_id,
                "to_id": to_id,
                "lat_from": origin[0],
                "lon_from": origin[1],
                "lat_to": destination[0],
                "lon_to": destination[1],
                "departure_time": departure_time,
                **self._get_travel_times(origin, destination, departure_time),
            }
        return travel_times

    def _get_travel_times_for_origin(
        self, origin_index: int, input_df: pd.DataFrame
    ) -> Dict[str, Any]:
        destinations = list(input_df.index)[: self.max_destination]
        # Remove destination that is equal to origin, i==j
        destinations.pop(origin_index)
        travel_times = {}
        for destination_index in destinations:
            travel_times.update(
                self._get_travel_times_for_pair(
                    origin_index, destination_index, input_df
                )
            )
        return travel_times

    def _get_travel_times_for_all_origins(
        self, input_df: pd.DataFrame
    ) -> Dict[str, Any]:
        travel_times = {}
        for origin_index in list(input_df.index)[: self.max_origin]:
            travel_times.update(
                self._get_travel_times_for_origin(origin_index, input_df)
            )
        return travel_times

    def _save_travel_times(self, travel_times: Dict[str, Any]) -> None:
        travel_times_flat = [flatten_dict(travel_times[tt]) for tt in travel_times]
        travel_times_df = pd.DataFrame(travel_times_flat)
        travel_times_df.to_csv(
            self.output_csv_path,
            index=False,
            mode="a",
            header=not os.path.exists(self.output_csv_path),
        )

    @measure_time
    def main(self) -> None:
        try:
            logger.info("Initializing variables")
            input_df = pd.read_csv(self.input_csv_path)
            logger.info(f"Nb rows in input file: {len(input_df)}")
            travel_times = self._get_travel_times_for_all_origins(input_df)
            self._save_travel_times(travel_times)
            logger.info("End of programm successfully")
        except Exception:
            e = sys.exc_info()[1]
            if e is not None:
                logger.exception(
                    f"End of programm with error {str(e.args[0])} - {e.args[1]}"
                )


def validate_keys(original: Dict, updates: Dict) -> None:
    original_keys = flatten_dict(original).keys()
    update_keys = flatten_dict(updates).keys()

    for key in update_keys:
        if key not in original_keys:
            raise KeyError(f"Invalid key: {key}")


def parse_args() -> Any:
    parser = argparse.ArgumentParser(description="Process some integers.")
    parser.add_argument(
        "--config", type=str, help="Path to the config file", required=True
    )
    parser.add_argument(
        "--start_times",
        type=str,
        action="append",
        help="Start date in the format YYYY-MM-DDTHH:MM:SS",
        required=True,
    )
    parser.add_argument(
        "--end_times",
        type=str,
        action="append",
        help="End date in the format YYYY-MM-DDTHH:MM:SS",
        required=True,
    )
    parser.add_argument(
        "--freq",
        type=str,
        default="10",
        help="Frequency of time steps in minutes, default to 10 minutes",
        required=False,
    )
    parser.add_argument(
        "--config_updates",
        type=str,
        help="JSON string of updates to the config",
        required=False,
    )
    parser.add_argument(
        "--use_real_time", action="store_true", help="Use real-time departure times"
    )
    return parser.parse_args()


def load_config(config_path: str) -> Dict:
    try:
        with open(config_path, "r") as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error(f"Config file not found: {config_path}")
        sys.exit(1)


def generate_departure_times(
    start_times: List[str], end_times: List[str], freq: str
) -> List[str]:
    departure_times = []
    for start_time, end_time in zip(start_times, end_times):
        departure_times.extend(
            pd.date_range(start=start_time, end=end_time, freq=f"{freq}min")
            .strftime("%Y-%m-%dT%H:%M:%S")
            .tolist(),
        )
    return departure_times


def update_config(config: Dict, config_updates: Optional[str]) -> None:
    if config_updates:
        config_updates_dict = json.loads(config_updates)
        validate_keys(config, config_updates_dict)
        config.update(config_updates_dict)


def setup_logging(config_file_path: str) -> None:
    try:
        os.makedirs(f"{os.getcwd()}\\logs", exist_ok=True)
    except OSError:
        logger.info("Directory was not created")
    logger.info(f"Starting programm with this config file: {config_file_path}")
    logger_path = f"{os.getcwd()}\\logs\\logs_{dt.now().strftime('%Y%m%dT%H%M%S')}.log"
    numeric_level = getattr(logging, "INFO", None)
    logging.basicConfig(
        level=numeric_level,
        format="%(asctime)-15s %(levelname)s: %(message)s",
        handlers=[logging.FileHandler(logger_path), logging.StreamHandler(sys.stdout)],
    )


if __name__ == "__main__":
    try:
        args = parse_args()
        config = load_config(args.config)
        departure_times = (
            None
            if args.use_real_time
            else generate_departure_times(args.start_times, args.end_times, args.freq)
        )
        update_config(config, args.config_updates)
        setup_logging(args.config)
        print(f"Departure times: {departure_times}")
        tool_object = GISCoreAPIRouting(args.config, departure_times)
        tool_object.main()
    except Exception:
        e = sys.exc_info()[1]
        if e is not None:
            logger.exception(
                f"End of programm with error {str(e.args[0])} - {e.args[1]}"
            )
